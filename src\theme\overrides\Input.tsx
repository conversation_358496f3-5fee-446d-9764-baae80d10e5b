// import { Components, Theme } from '@mui/material';

// export default function Input(theme: Theme): Components<Theme> {
//   return {
//     MuiInputBase: {
//       styleOverrides: {
//         root: {
//           '&.Mui-disabled': {
//             '& svg': { color: theme.palette.text.disabled },
//           },
//           '&.MuiInputBase-multiline': {
//             padding: 0,
//           },
//           [`@media (max-width:576px)`]: {
//             // fontSize: '1.05rem',
//              minHeight: 45,
//             // padding: '0.57rem 0.875rem',
//           },
//         },
//         // ✅ Correct way to target sizeSmall
//         sizeSmall: {
//           [`@media (max-width:576px)`]: {
//             minHeight: 30,
//           },
//         },
//         input: {
//           '&::placeholder': {
//             opacity: 1,
//             color: theme.palette.text.disabled,
//           },
//           // [`@media (max-width:576px)`]: {
//           // padding: '0.7rem 1rem',
//           // },
//         },
//       },
//     },

//     MuiInput: {
//       styleOverrides: {
//         underline: {
//           '&:before': {
//             borderBottomColor: theme.palette.grey[500_56],
//           },
//           fullWidth: {
//             width: '100%',
//           },
//         },
//         root: {
//           [`@media (max-width:576px)`]: {
//             // fontSize: '1.05rem',
//             minHeight: 45,
//           },
//         },
//         input: {
//           [`@media (max-width:576px)`]: {
//             // padding: '0.7rem 1rem',
//           },
//         },
//       },
//     },

//     MuiFilledInput: {
//       styleOverrides: {
//         root: {
//           backgroundColor: theme.palette.grey[500_12],
//           '&:hover': {
//             backgroundColor: theme.palette.grey[500_16],
//           },
//           '&.Mui-focused': {
//             backgroundColor: theme.palette.action.focus,
//           },
//           '&.Mui-disabled': {
//             backgroundColor: theme.palette.action.disabledBackground,
//           },
//           [`@media (max-width:576px)`]: {
//             // fontSize: '1.05rem',
//             minHeight: 45,
//           },
//         },
//         underline: {
//           '&:before': {
//             borderBottomColor: theme.palette.grey[500_56],
//           },
//         },
//         input: {
//           [`@media (max-width:576px)`]: {
//             // padding: '0.7rem 1rem',
//           },
//         },
//       },
//     },

//     MuiOutlinedInput: {
//       styleOverrides: {
//         root: {
//           fullWidth: 'true',
//           fontSize: '0.875rem',
//           '& .MuiOutlinedInput-notchedOutline': {
//             borderWidth: '1.5px!important',
//           },
//           '&.Mui-disabled': {
//             '& .MuiOutlinedInput-notchedOutline': {
//               borderColor: theme.palette.action.disabledBackground,
//             },
//           },
//           '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
//             borderColor: theme.palette.primary.main,
//             outline: 0,
//             boxShadow: theme.palette.primary.main,
//           },
//           ':hover .MuiOutlinedInput-notchedOutline': {
//             borderColor: theme.palette.primary.main,
//           },
//           '&.Mui-error .MuiOutlinedInput-notchedOutline': {
//             borderColor: theme.palette.error,
//           },
//           '&.Mui-error .MuiOutlinedInput-notchedOutline:hover': {
//             borderColor: theme.palette.error,
//           },
//           [`@media (max-width:576px)`]: {
//             // fontSize: '1.05rem',
//             // minHeight: 45,
//           },
//         },
//         input: {
//           padding: '0.57rem 0.675rem',
//           [`@media (max-width:576px)`]: {
//             // padding: '0.7rem 1rem',
//             // padding: '0.57rem 0.675rem',
//           },
//         },
//         inputSizeSmall: {
//           // padding: '0.5rem 0.875rem',
//           [`@media (max-width:576px)`]: {
//             // padding: '0.6rem 0.9rem',
//           },
//         },
//       },
//     },
//   };
// }

import { Components, Theme } from '@mui/material';

export default function Input(theme: Theme): Components<Theme> {
  return {
    MuiInputBase: {
      styleOverrides: {
        root: {
          '&.Mui-disabled': {
            '& svg': { color: theme.palette.text.disabled },
          },
          '&.MuiInputBase-multiline': {
            padding: 0,
          },
        },
        input: {
          '&::placeholder': {
            opacity: 1,
            color: theme.palette.text.disabled,
          },
        },
      },
    },
    MuiInput: {
      styleOverrides: {
        underline: {
          '&:before': {
            borderBottomColor: theme.palette.grey[500_56],
          },
          fullWidth: {
            width: '100%',
          },
        },
      },
    },
    MuiFilledInput: {
      styleOverrides: {
        root: {
          backgroundColor: theme.palette.grey[500_12],
          '&:hover': {
            backgroundColor: theme.palette.grey[500_16],
          },
          '&.Mui-focused': {
            backgroundColor: theme.palette.action.focus,
          },
          '&.Mui-disabled': {
            backgroundColor: theme.palette.action.disabledBackground,
          },
        },
        underline: {
          '&:before': {
            borderBottomColor: theme.palette.grey[500_56],
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          fullWidth: 'true',
          fontSize: '0.875rem',
          '& .MuiOutlinedInput-notchedOutline': {
            borderWidth: '1.5px!important',
          },
          '&.Mui-disabled': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: 'theme.palette.action.disabledBackground',
            },
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.palette.primary.main,
            outline: 0,
            boxShadow: theme.palette.primary.main,
          },
          ':hover .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.palette.primary.main,
          },

          '&.Mui-error .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.palette.error,
          },
          '&.Mui-error .MuiOutlinedInput-notchedOutline:hover': {
            borderColor: theme.palette.error,
          },
        },
        input: {
          padding: '0.57rem 0.875rem',
          // height: 30,
        },
        inputSizeSmall: {
          padding: '0.57rem 0.875rem',
        },
      },
    },
  };
}
