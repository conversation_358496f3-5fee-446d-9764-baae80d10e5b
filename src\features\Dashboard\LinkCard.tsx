import styled from 'styled-components';
import { Card, CardContent, Typography, CardActionArea, Grid, Box } from '@mui/material';
import { LinkCardData } from '@/config/LinkCard';
import { Link } from 'react-router-dom';
import { useTheme } from '@mui/material';

const LinkCardRoot = styled.div`
  padding: 1rem 0.5rem 1rem 0.5rem;
  /* margin-top: 1rem; */
  /* margin-bottom: 1rem; */
  .cards {
    transition: 0.5s;
  }
  .icon {
    transition: 0.5s;
  }
  .cards:hover {
    transform: scale(1.03);
  }
  .icon:hover {
    transform: scale(1.1);
  }
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .icon-circle {
    border-radius: 100%;
  }

  a {
    text-decoration: none;
    color: black;
  }
`;

function LinkCard() {
  const theme = useTheme();
  // Converts hex color to rgba with given alpha
  function hexToRgba(hex: string, alpha: number) {
    let c = hex.replace('#', '');
    if (c.length === 3) c = c[0] + c[0] + c[1] + c[1] + c[2] + c[2];
    const num = parseInt(c, 16);
    return `rgba(${(num >> 16) & 255}, ${(num >> 8) & 255}, ${num & 255}, ${alpha})`;
  }
  return (
    <LinkCardRoot className="linkcard">
      <Box sx={{ width: '100%' }}>
        <Grid container spacing={2}>
          {LinkCardData?.map((item) => (
            <Grid item xl={2} xs={4} key={item.id}>
              <Link to={`${item.link}`}>
                <Card
                  className="cards"
                  sx={{
                    background: item.color,
                    // boxShadow: `0 14px 16px 0 ${hexToRgba(item.color, 0.25)}`
                  }}
                  // sx={{ backgroundColor: item.color }}
                  // sx={{
                  //   background: theme.themeMode === 'light' ? item.color : theme.palette.grey[50032],
                  // }}
                >
                  {/* First SVG (top wave) */}
                  {/* <svg
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      pointerEvents: 'none',
                      // rotate: '10deg',
                    }}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 1440 320"
                    preserveAspectRatio="none"
                  >
                    <path fill="#fff" fillOpacity=".1" d="M0,224L1440,0L1440,0L0,0Z" />
                  </svg> */}

                  <svg
                    style={{
                      position: 'absolute',
                      top: -8,
                      left: 0,
                      pointerEvents: 'none',
                      // rotate: '10deg',
                    }}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 1440 320"
                    preserveAspectRatio="none"
                  >
                    <path fill="#fff" fillOpacity=".1" d="M0,32L1440,256L1440,0L0,0Z" />
                  </svg>

                  {/* Second SVG (bottom wave) */}
                  <svg
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      pointerEvents: 'none',
                    }}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 1440 320"
                    preserveAspectRatio="none"
                  >
                    <path fill="#fff" fillOpacity=".1" d="M0,64L1440,320L1440,0L0,0Z" />
                  </svg>
                  <CardActionArea>
                    <CardContent className="content">
                      <div className="icon-circle bg-white p-2 mb-1">
                        <img src={item.icon} className="icon" alt="" />
                      </div>
                      <Typography
                        sx={{
                          color: theme.palette.common.white,
                          fontFamily: 'Poppins Semibold',
                          // fontSize: '100%',
                          fontSize: { xs: 10, sm: 12 },
                          // display: { xs: 'none', sm: 'block' },
                        }}
                      >
                        {item.label}
                      </Typography>
                      {/* <Typography
                        variant="subtitle2"
                        sx={{
                          color: '#fff',
                          fontFamily: 'Poppins Semibold',
                          fontSize: 10,
                          // fontWeight: '800',
                          display: { sm: 'none', xs: 'block' },
                        }}
                      >
                        {item.label}
                      </Typography> */}
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Link>
            </Grid>
          ))}
        </Grid>
      </Box>
    </LinkCardRoot>
  );
}

export default LinkCard;
