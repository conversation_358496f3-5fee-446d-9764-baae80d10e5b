import SideBar from '@/features/mainlayout/SideBar/SideBar';
import TopBar from '@/features/mainlayout/TopBar';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, Outlet } from 'react-router-dom';
import styled, { useTheme } from 'styled-components';
import { breakPointsMinwidth } from '@/config/breakpoints';
// import TreeMenu from '@/features/mainlayout/SideBar/TreeMenu';
// import { TreeMenuItem } from '@/types/Layout';
import { AnimatePresence } from 'framer-motion';
import SelectedListItem from '@/features/mainlayout/SideBar/ListView';
import Switch from '@mui/material/Switch';
import { useEffect, useRef, useState } from 'react';
import { Stack, Typography, ToggleButtonGroup, ToggleButton } from '@mui/material';
import ParentSelectedListItem from '@/features/mainlayout/SideBar/ParentListView';
import useAuth from '@/hooks/useAuth';

const Content = styled.section<{ $topBarHeight: number }>`
  padding-top: ${({ $topBarHeight }) => `${$topBarHeight}px`};
  @media ${breakPointsMinwidth.md} {
    padding-left: ${SIDE_BAR_WIDTH};
  }
  overflow-x: hidden;
`;

function MainLayout() {
  const topBarRef = useRef<HTMLElement>(null);
  const [topBarHeight, setTopBarHeight] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const { parentMode } = useAuth();

  useEffect(() => {
    if (topBarRef.current) {
      setTopBarHeight(topBarRef.current.offsetHeight);
    }
    const handleResize = () => {
      if (topBarRef.current) {
        setTopBarHeight(topBarRef.current.offsetHeight);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // const handleMenuItemClick = useCallback(
  //   (list: ListMenuItem) => {
  //     console.log(list);
  //     if (list && list.link && !window.matchMedia(breakPointsMinwidth.md).matches) {
  //       dispatch(toggleSidebar());
  //     }
  //   },
  //   [dispatch]
  // );
  // const [adminView, setAdminView] = useState(true);
  console.log('Parent Mode:', parentMode);
  return (
    <main className="passdaily-mainLayout-root">
      <TopBar ref={topBarRef} />
      <SideBar topBarHeight={topBarHeight} searchQuery={searchQuery} onSearchChange={setSearchQuery}>
        {!parentMode ? (
          <SelectedListItem searchQuery={searchQuery} onSearchChange={setSearchQuery} />
        ) : (
          <ParentSelectedListItem searchQuery={searchQuery} onSearchChange={setSearchQuery} />
        )}
      </SideBar>
      <Content $topBarHeight={topBarHeight}>
        <AnimatePresence mode="wait">
          <Outlet context={{ topBarHeight }} />
        </AnimatePresence>
      </Content>
    </main>
  );
}

export default MainLayout;
