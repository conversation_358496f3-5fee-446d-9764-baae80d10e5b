/* eslint-disable @typescript-eslint/no-use-before-define */
import styled, { ThemeConsumer } from 'styled-components';
import {
  Card,
  Typography,
  Stack,
  Box,
  useTheme,
  SelectChangeEvent,
  Select,
  MenuItem,
  Tooltip,
  Skeleton,
  Chip,
} from '@mui/material';
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation, Grid } from 'swiper';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import NodatatimetableIcon from '@/assets/timetable/timetable.svg';

import { breakPointsMaxwidth } from '@/config/breakpoints';
import typography from '@/theme/typography';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { DashboardTimeTableType } from '@/types/Dashboard';
import { getClassData, getClassStatus, getTimeTableData, getTimeTableStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { fetchClassList, fetchDashboardTimeTable } from '@/store/Dashboard/dashboard.thunks';
import { ClassListInfo } from '@/types/AcademicManagement';
import { TimetableIconMap } from '@/config/TimetableIconMap';
import { TimetableDatas } from '@/config/TimetableData';

import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import AnimatedProgressBar from '@/components/shared/RND/ProgressBarAnimation';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 700],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.primary.main : '#fff',
  },
}));

const TimetableRoot = styled.div`
  /* padding: 1rem; */
  .heading {
    padding: 1rem;
  }
  .period {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.white};
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.common.black};
    border-radius: 20px;
    border: 1.5px solid
      ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.black};
  }
  .swiper_container {
    position: relative;
    padding: 1rem 2.2rem 1rem 2.2rem;
  }
  .swiper-slide {
    position: relative;
  }
  .swiper-button-prev {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.primary.main};
    transform: translateX(-8px);
  }

  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.primary.main};
    transform: translateX(8px);
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(5px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .timetable-card {
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]}; */
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[800])};
  }
  .swiper-pagination {
    position: relative;
    transform: translateY(10px);
  }
  @media ${breakPointsMaxwidth.sm} {
    .swiper_container {
      padding: 1rem;
    }
  }
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  .scrolling-text {
    animation: scroll 10s linear infinite;
    white-space: nowrap;
    overflow: hidden;
  }
  .MuiSelect-outlined {
    /* border: 1px solid red; */
    /* border-radius: 50px; */
    padding: 0px 10px 0px 10px;
    /* height: 5px; */
  }
`;

export default function Timetable({ ClassData }: any) {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const TimeTableData = useAppSelector(getTimeTableData);

  const TimeTableStatus = useAppSelector(getTimeTableStatus);

  const AllClassOption = {
    classId: -1,
    className: `All Class`,
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];

  const [classOptions, setClassOptions] = useState<ClassListInfo | null>(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  // const fetchListClass = useCallback(() => {
  //   setClassOptions(ClassData[0]);
  // }, [ClassData]);

  useEffect(() => {
    // setClassOptions(ClassData[0]);
    // fetchListClass();
    dispatch(fetchDashboardTimeTable({ adminId, classId }));
  }, [dispatch, adminId, classId]);

  const getOrdinalSuffix = (number: number) => {
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const v = number % 100;
    return v >= 11 && v <= 13 ? 'th' : suffixes[(v - 20) % 10] || suffixes[v] || 'th';
  };

  return (
    <TimetableRoot>
      <Box className="">
        <Stack direction="row" sx={{ padding: ' 1rem 1rem 0rem 1rem' }} display="flex" justifyContent="space-between">
          <Typography variant="h6" fontSize={18} sx={{ fontFamily: typography.fontFamily }}>
            Time Table
          </Typography>
          {/* <SelectBox Selection_Options={CLASS_SELECT} placeholder="Class" /> */}
          <Select
            sx={{
              // backgroundColor: theme.palette.primary.lighter,
              // color: theme.palette.primary.main,
              height: 30,
            }}
            value={className}
            onChange={handleChange}
            variant="outlined"
            displayEmpty
            labelId="demo-dialog-select-label"
            id="demo-dialog-select"
            inputProps={{ 'aria-label': 'Without label' }}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: '250px', // Adjust the value to your desired height
                },
              },
            }}
          >
            {/* <MenuItem value={className} className="d-none"> 
              {className}
            </MenuItem> */}
            {classDataWithAllClass?.map((item: ClassListInfo) => (
              <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                {item.className}
              </MenuItem>
            ))}
          </Select>
        </Stack>
        <Swiper
          spaceBetween={17}
          effect="coverflow"
          grabCursor
          breakpoints={{
            299: {
              slidesPerView: 1.2,
            },
            499: {
              slidesPerView: 2,
            },
            699: {
              slidesPerView: 3,
            },
            766: {
              slidesPerView: 2,
            },
            999: {
              slidesPerView: 2,
            },
            1180: {
              slidesPerView: 3,
            },
            1499: {
              slidesPerView: 4,
            },
            1699: {
              slidesPerView: 5,
            },
          }}
          coverflowEffect={{
            rotate: 0,
            depth: 0,
            stretch: 0,
          }}
          pagination={{ el: '.swiper-pagination', clickable: true }}
          navigation={{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }}
          modules={[Pagination, Navigation]}
          className="swiper_container"
        >
          {TimeTableStatus === 'loading' ? (
            [1, 2, 3, 4, 5]?.map((index) => (
              <SwiperSlide className="swiper-slide" key={index}>
                <Card className="timetable-card" sx={{ padding: '10px', boxShadow: '0' }}>
                  <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                    <Skeleton variant="circular" width={40} height={40} />
                    <Skeleton variant="rounded" width={70} height={15} />
                  </Stack>
                  <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                    <Skeleton variant="rounded" width={100} height={15} />
                    <Skeleton variant="rounded" width={50} height={15} />
                  </Stack>
                  <Stack spacing={1} mt={2.5}>
                    <Skeleton variant="rounded" width={50} height={12} />
                    <Skeleton variant="rounded" width="100%" height={12} />
                  </Stack>
                </Card>
              </SwiperSlide>
            ))
          ) : (
            <div>
              {TimeTableData.length === 0 ? (
                <Stack direction="column" justifyContent="center" alignItems="center" spacing={1} mb={1}>
                  <img width={120} src={NodatatimetableIcon} alt="" style={{ opacity: 0.6 }} />
                  <Typography variant="subtitle2" color="secondary">
                    No Timetable added in this Class.
                  </Typography>
                </Stack>
              ) : (
                TimeTableData?.map((data, index) => {
                  const { subjectIcon } = data;
                  const iconPath = subjectIcon ? subjectIcon.replace(/\.png/, '') : null;
                  const Icon = iconPath ? TimetableIconMap[iconPath] : null;
                  const delay = (index + 1) * 50;
                  return (
                    <SwiperSlide className="swiper-slide" key={data.ttableId}>
                      <Card className="timetable-card" sx={{ padding: '10px', boxShadow: '0' }}>
                        <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
                          <div>{Icon && <img width={35} src={Icon} className="" alt={Icon} />}</div>
                          <div>
                            <Typography className="period px-2" fontSize={12} fontWeight={600}>
                              {data.periodName}
                              {getOrdinalSuffix(data.periodName)}&nbsp;Period
                            </Typography>
                            {/* <Chip
                              variant="outlined"
                              className=""
                              color="primary"
                              sx={{ height: 20, fontWeight: 500 }}
                              label={`${data.periodName}${getOrdinalSuffix(data.periodName)} Period`}
                            /> */}
                          </div>
                        </Stack>

                        <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
                          <Tooltip
                            title={data.subjectName
                              .split(' ')
                              .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                              .join(' ')}
                          >
                            <Typography
                              fontSize={14}
                              fontWeight={600}
                              className=""
                              sx={{
                                maxWidth: { xxl: '70%', xl: '125px', lg: '100px', md: '50%', sm: '50%', xs: '70%' },
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {data.subjectName
                                .split(' ')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                                .join(' ')}
                            </Typography>
                          </Tooltip>
                          <Typography fontSize={14} fontWeight={600}>
                            {data.className}
                          </Typography>
                        </Stack>
                        <Typography fontSize={12} fontWeight={500} marginTop={2} color="#818594">
                          {/* {data.} */}Chapter
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ width: '100%', mr: 1 }}>
                            {/* <BorderLinearProgress variant="determinate" value={50} sx={{ marginTop: '' }} /> */}
                            <AnimatedProgressBar targetPercentage={60} delay={delay} />
                          </Box>
                          <Box>
                            {/* <Typography variant="body2" color="text.secondary">
                              60%
                            </Typography> */}
                          </Box>
                        </Box>
                      </Card>
                    </SwiperSlide>
                  );
                })
              )}
            </div>
          )}
          <div className="slider-controler d-none d-sm-flex">
            <div>
              <KeyboardArrowLeftIcon className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle" />
            </div>
            <div>
              <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 shadow card rounded-circle" />
            </div>
            {/* <div className="swiper-pagination d-none"> </div> */}
          </div>
        </Swiper>
      </Box>
    </TimetableRoot>
  );
}
