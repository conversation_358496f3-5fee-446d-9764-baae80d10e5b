/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { VitePWA } from 'vite-plugin-pwa';

const projectRootDir = resolve(__dirname);

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      manifest: {
        name: 'passdaily',
        short_name: 'Passdaily',
        icons: [
          {
            src: '/logo-small.svg',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any',
          },
          {
            src: '/logo-small.svg',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable',
          },
        ],
      },
    }),
  ],
  server: {
    open: true,
    port: 8080,
  },
  resolve: {
    alias: {
      '@': resolve(projectRootDir, 'src'),
    },
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: (assetInfo) => {
          const ext = assetInfo.name.split('.').pop();
          if (ext === 'css') return `assets/[name].css`;
          return `assets/[name].[ext]`;
        },
      },
    },
  },
});
