/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
  Paper,
  Tooltip,
  IconButton,
  Collapse,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  Skeleton,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassData,
  getClassSectionsData,
  getFeePaidListData,
  getFeePaidListStatus,
  getFeePaidTermListData,
  getFeePaidTermListStatus,
  getManageFeeclassListData,
  getStudentFilterData,
  getTermFeeFilterData,
  getYearData,
} from '@/config/storeSelectors';
import dayjs from 'dayjs';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import { fetchFeePaidList, fetchFeePaidTermList, fetchTermFeeFilter } from '@/store/ManageFee/manageFee.thunks';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { useSchool } from '@/contexts/SchoolContext';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';

const DailyReportRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiTableCell-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
`;

export default function DailyReport() {
  const tableRef = useRef<HTMLDivElement>(null);
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const [showFilter, setShowFilter] = useState(true);
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const { selectedSchool } = useSchool();

  const receiptType = 'ReceiptDefault';

  const receiptForPrintListStatus = useAppSelector(getFeePaidListStatus);
  const YearData = useAppSelector(getYearData);
  const feePaidTermListData = useAppSelector(getFeePaidTermListData);
  // const classListData = useAppSelector(getManageFeeclassListData);
  const ClassData = useAppSelector(getClassData);
  const studentFilterData = useAppSelector(getStudentFilterData);
  const termFeeFilterData = useAppSelector(getTermFeeFilterData);
  const feePaidTermListStatus = useAppSelector(getFeePaidTermListStatus);

  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [classFilter, setClassFilter] = useState(0);
  const [termFeeFilter, setTermFeeFilter] = useState(-1);
  const [fromDateFilter, setFromDateFilter] = useState('');
  const [toDateFilter, setToDateFilter] = useState('');
  const [totalAmount, setTotalAmount] = useState<number>();

  const currentFeePaidListRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      classId: classFilter,
      fromDate: fromDateFilter,
      toDate: toDateFilter,
    }),
    [adminId, academicYearFilter, classFilter, fromDateFilter, toDateFilter]
  );

  const loadFeeDailyReportList = useCallback(
    async (request: any) => {
      try {
        const response = await dispatch(fetchFeePaidTermList(request)).unwrap();

        if (response) {
          const TotalAmnt = response.map((m) => m.totalPaid)[0];
          setTotalAmount(TotalAmnt);
          // Handle the response if needed
          console.log('response::::----', response);
        }
      } catch (error) {
        // Log the error or handle it appropriately
        console.error('Failed to load term fee list:', error);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList(adminId));
    dispatch(fetchTermFeeFilter({ adminId, academicId: academicYearFilter, feeTypeId: 1 }));
    if (feePaidTermListStatus === 'idle') {
      loadFeeDailyReportList(currentFeePaidListRequest);
    }
  }, [
    loadFeeDailyReportList,
    feePaidTermListStatus,
    currentFeePaidListRequest,
    adminId,
    dispatch,
    classFilter,
    academicYearFilter,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeeDailyReportList({ ...currentFeePaidListRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(ClassData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadFeeDailyReportList({ ...currentFeePaidListRequest, classId: parseInt(e.target.value, 10) });
  };

  const handleTermFeeTitleChange = (e: SelectChangeEvent) => {
    setTermFeeFilter(parseInt(termFeeFilterData.filter((item) => item.termId === e.target.value)[0].termId, 10));
    loadFeeDailyReportList({ ...currentFeePaidListRequest, termId: parseInt(e.target.value, 10) });
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setFromDateFilter('');
      setToDateFilter('');
      setClassFilter(0);
      setAcademicYearFilter(0);
      loadFeeDailyReportList({
        adminId,
        academicId: academicYearFilter,
        feeTypeId: 0,
        sectionId: 0,
        classId: 0,
        studentId: -1,
        fromDate: '',
        toDate: '',
      });
    },
    [loadFeeDailyReportList, adminId, academicYearFilter]
  );

  const getRowKey = useCallback((row: any, index: number | undefined) => index, []);

  const feePaidListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: '',
        headerLabel: 'SL.No',
        renderCell: (_, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {index + 1}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.studentName}
            </Typography>
          );
        },
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Typography minWidth={100} fontSize={13} variant="subtitle1">
              {row.admissionNo}
            </Typography>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'feeTitle',
        headerLabel: 'Fee Title',
        renderCell: (row) => {
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.feeTitle}
            </Typography>
          );
        },
      },
      {
        name: 'termTitle',
        headerLabel: 'Term Title',
        renderCell: (row) => {
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.termTitle}
            </Typography>
          );
        },
      },
      {
        name: 'paidDate',
        headerLabel: 'Paid Date',
        renderCell: (row) => {
          const formattedDate = dayjs(row.paidDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, h:mm a');
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Daily Report">
      <DailyReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Daily Report
            </Typography>
            <Stack direction="row" alignItems="center">
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={{ xxl: 8, lg: 12, sm: 32 }} pt={1} container columnSpacing={2} alignItems="end">
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        // disabled={ClassData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {ClassData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Term Fee Title
                      </Typography>
                      <Select
                        // disabled={studentFilterData.length === 0}
                        labelId="termFeeFilter"
                        id="termFeeFilter"
                        value={termFeeFilter?.toString()}
                        onChange={handleTermFeeTitleChange}
                        placeholder="Select Fee"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1} sx={{ display: 'none' }}>
                          Select Term Fee
                        </MenuItem>
                        {termFeeFilterData.map((opt) => (
                          <MenuItem key={opt.termId} value={opt.termId}>
                            {opt.termTitle}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DatePickers
                        // disabled={studentFilterData.length === 0}
                        name="fromDateFilter"
                        value={dayjs(fromDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setFromDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          loadFeeDailyReportList({ ...currentFeePaidListRequest, fromDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DatePickers
                        // disabled={studentFilterData.length === 0}
                        name="toDateFilter"
                        value={dayjs(toDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setToDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          loadFeeDailyReportList({ ...currentFeePaidListRequest, toDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button sx={{ mt: { xs: 2 } }} type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box textAlign="center" pb={2}>
              {receiptForPrintListStatus === 'loading' ? (
                <Box
                  flexDirection="column"
                  display="flex"
                  gap={1}
                  alignItems="center"
                  //  mb={receiptType ? 2 : 2}
                >
                  <Skeleton variant="circular" width={70} height={70} />
                  <Skeleton variant="rounded" width={180} height={15} />
                  <Skeleton variant="rounded" width={230} height={15} />
                  <Skeleton variant="rounded" width={300} height={15} />
                </Box>
              ) : (
                <Box
                  flexDirection="column"
                  display="flex"
                  alignItems="center"
                  // mb={receiptType ? 2 : 0}
                >
                  {receiptType === 'ReceiptDefault' && (
                    <img src={selectedSchool?.schoolLogo} alt={selectedSchool?.schoolLogo} width={70} />
                  )}
                  {/* <img src={passdailLogo} width={70} alt="passdailLogo" /> */}
                  {/* {receiptType === 'ReceiptDefault' && <img src={holyLogo} width={70} alt="holyLogo" />} */}
                  {/* <img src={carmelLogo} width={70} alt="carmelLogo" /> */}
                  {/* <img src={thereseLogo} width={70} alt="thereseLogo" /> */}
                  {/* <img src={thomasLogo} width={70} alt="thomasLogo" /> */}
                  {/* <img src={nirmalaLogo} width={70} alt="nirmalaLogo" /> */}
                  {/* <img src={MIMLogo} width={70} alt="MIMLogo" /> */}

                  {/* {receiptType === 'ReceiptDefault' && (
              <>
                <Typography variant="subtitle2" fontSize={16} color="primary">
                  {schoolName}
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  {schoolAddress}
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  {schoolEmail}, {schoolPhone}
                </Typography>
              </>
            )} */}

                  {receiptType === 'ReceiptDefault' &&
                    selectedSchool?.address &&
                    typeof selectedSchool.address === 'function' &&
                    selectedSchool.address()}
                  {/* ========== MIM HIGH SCHOOL ========== */}
                  {/* <>
              <Typography variant="subtitle2" fontSize={16} color="primary">
                Mueenul Islam Manoor High School
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                KANDANAKAM,KALADI PO MALAPPURAM 679582
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                04942103095, 9645942121
              </Typography>
            </> */}
                  {/* ========== MIM HIGH SCHOOL ========== */}
                  {/* <>
              <Typography variant="subtitle2" fontSize={16} color="primary">
                Assisi English Medium Higher Secondary School
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                Pudussery Kanjikode, Palakkad 678621
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                0491256679, 9496069179
              </Typography>
            </> */}
                </Box>
              )}
              {/* ========== Holy Angels Gardens ========== */}
              {receiptType === 'ReceiptHolyNursery' && (
                <Box
                  className="Main-div"
                  flexDirection="row"
                  display="flex"
                  mt={5}
                  gap={4}
                  justifyContent="center"
                  alignItems="center"
                  p={2}
                  mb={2}
                  bgcolor="#e2b4bb"
                >
                  <img src={holyLogo} width={100} alt="holyLogo" />
                  <Stack textAlign="center" flex={1}>
                    <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                      <b>ANGELS&apos; GARDENS</b>
                    </Typography>
                    <Typography variant="body1" fontSize={13}>
                      The Kindergarten Experience
                    </Typography>
                    <Typography variant="body1" fontSize={15}>
                      Gandhinagar, Dombivli (E)
                    </Typography>
                  </Stack>
                  <div style={{ visibility: 'hidden', width: 100 }} />
                </Box>
              )}
              {/* ========== Holy Angels Paradise Kindergarten ========== */}
              {/* ITH */}
              {receiptType === 'ReceiptHolyKg' && (
                <Box
                  className="Main-div"
                  flexDirection="row"
                  display="flex"
                  mt={5}
                  gap={4}
                  justifyContent="center"
                  alignItems="center"
                  p={2}
                  mb={2}
                  bgcolor="#e2b4bb"
                >
                  <img src={holyLogo} width={100} alt="holyLogo" />
                  <Stack textAlign="center" flex={1}>
                    <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                      <b>ANGELS&apos; PARADISE</b>
                    </Typography>
                    <Typography variant="body1" fontSize={13}>
                      The Kindergarten Experience
                    </Typography>
                    <Typography variant="body1" fontSize={15}>
                      Gandhinagar, Dombivli (E)
                    </Typography>
                  </Stack>
                  <div style={{ visibility: 'hidden', width: 100 }} />
                </Box>
              )}
              {/* ========== Holy Angels Paradise College ========== */}
              {receiptType === 'ReceiptHolyCollege' && (
                <Box
                  className="Main-div"
                  flexDirection="column"
                  mt={5}
                  bgcolor="yellow"
                  border={1}
                  p={1}
                  display="flex"
                  alignItems="center"
                  mb={2}
                >
                  <img src={holyLogo} width={70} alt="holyLogo" />
                  <Stack color={theme.palette.common.black} direction="row" justifyContent="space-between" gap={5}>
                    <Typography variant="subtitle2" fontSize={14}>
                      Affiliated to CBSE
                    </Typography>
                    <Typography variant="subtitle2" fontSize={13}>
                      TRINITY EDUCATIONAL TRUST&apos;S
                    </Typography>
                    <Typography variant="subtitle2" fontSize={14}>
                      ISO 9001: 2008 Certified
                    </Typography>
                  </Stack>
                  <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                    <b>HOLY ANGELS&apos; SCHOOL & Jr. COLLEGE</b>
                  </Typography>
                  <Typography color={theme.palette.common.black} variant="body1" fontSize={13}>
                    (A Private Unaided Minority Institution)
                  </Typography>
                  <Typography color={theme.palette.common.black} variant="body1" fontSize={9}>
                    Behind P & T Colony, Nandivli - Gandhinagar, Dombivli (E) Dist. Thane, MAH
                    <EMAIL> Tel: (0251) 2821975, 2821234
                  </Typography>
                </Box>
              )}
              {/* ============================ */}
            </Box>

            <Card
              className="TableCard"
              sx={{
                boxShadow: 0,
              }}
            >
              {/* <Paper sx={{ display: 'none' }}>
                <div ref={tableRef}>
                  <DataTable
                    tableStyles={{ minWidth: { xs: '1300px' } }}
                    columns={feePaidListColumns}
                    data={feePaidTermListData}
                    getRowKey={getRowKey}
                    fetchStatus="success"
                    // fetchStatus={feePaidTermListStatus}
                    showHorizontalScroll
                    // PaginationProps={pageProps}
                    // allowPagination
                  />
                </div>
              </Paper> */}
              <Paper
                className="TableCard"
                sx={{
                  boxShadow: 0,
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    width: { xs: '75.75rem', md: '100%' },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>SL.No</TableCell>
                        <TableCell>Fees Name</TableCell>
                        <TableCell>Total Payment(INR)</TableCell>
                        <TableCell>Total Discount(INR)</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[1, 2, 3, 4, 5].map((row) => (
                        <TableRow hover key={row}>
                          <TableCell>01</TableCell>
                          <TableCell>Admission Fee</TableCell>
                          <TableCell>5500</TableCell>
                          <TableCell>500</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
              <Box
                sx={{ borderTop: '1.5px dashed gray', pl: '12%', pr: '50%' }}
                display="flex"
                justifyContent="space-between"
                py={2}
              >
                <Typography variant="body2" fontWeight={600}>
                  Total
                </Typography>
                <Typography variant="body2" fontWeight={600}>
                  20,000
                </Typography>
              </Box>
            </Card>
          </div>

          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              variant="contained"
              size="medium"
              color="secondary"
              sx={{ color: '#fff', backgroundColor: theme.palette.secondary.main }}
            >
              Print
            </Button>
          </Box>
        </Card>
      </DailyReportRoot>
    </Page>
  );
}
