import { breakPointsMinwidth } from '@/config/breakpoints';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getSideBarState, toggleSidebar } from '@/store/Layout/layout.slice';
import { Drawer, Typography, Divider, Box, Stack, TextField, InputAdornment, useTheme, Avatar } from '@mui/material';
import React, { forwardRef, ReactNode, useState } from 'react';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import IconButton from '@mui/material/IconButton';
import { useSchool } from '@/contexts/SchoolContext';
import useSettings from '@/hooks/useSettings';
// import passdailyLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
// import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
// import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
// import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
// import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
// import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
// import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';
import { FiLogOut, FiSearch } from 'react-icons/fi';
import LogoutBox from '@/components/shared/Popup/LogoutBox';
import useAuth from '@/hooks/useAuth';
import { Account } from '@/types/Auth';

const SideBarRoot = styled.aside`
  position: fixed;
  top: ${TOP_BAR_HEIGHT};
  left: 0;
  width: 0;
  height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  box-shadow: ${(props) =>
    props.theme.themeMode === 'light' ? '0 1px 2px 0 rgb(0 0 0 / 5%)' : '0px 3px 9px #********'};
  border-right: ${(props) =>
    props.theme.themeMode === 'light' ? '1px solid rgb(185 182 182 / 18%)' : '1px solid rgb(0 0 0 / 18%)'};

  z-index: ${(props) => props.theme.zIndex.appBar - 10};

  @media ${breakPointsMinwidth.md} {
    width: ${SIDE_BAR_WIDTH};
  }
  a {
    text-decoration: none;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[900] : props.theme.palette.white};
  }
`;

export type SideBarProps = {
  window?: () => Window;
  children: ReactNode;
  topBarHeight?: any;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
};

function getInitials(user: Account) {
  if (user.firstName && user.lastName) {
    return `${user.firstName.trim().slice(0, 1).toUpperCase()}${user.lastName.slice(0, 1).toUpperCase()}`;
  }

  if (user.firstName) {
    if (user.firstName.trim().indexOf(' ') > -1) {
      const [part1, part2] = user.firstName.trim().split(' ');
      return `${part1.trim().slice(0, 1).toUpperCase()}${part2.slice(0, 1).toUpperCase()}`;
    }
    return `${user.firstName.trim().slice(0, 1).toUpperCase()}`;
  }

  return '';
}

function getDiplayName(user: Account) {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }

  if (user.firstName) {
    return `${user.firstName}`;
  }

  return 'Unknown Name';
}

const SideBar = forwardRef<HTMLElement, SideBarProps>((props, ref) => {
  const { window, children, topBarHeight, searchQuery = '', onSearchChange } = props;
  const { user } = useAuth();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  const sideBarOpen = useAppSelector(getSideBarState);
  const dispatch = useAppDispatch();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [popup, setPopup] = React.useState(false);

  const container = window !== undefined ? () => window().document.body : undefined;

  const { selectedSchool } = useSchool();

  const handleDrawerToggle = () => {
    dispatch(toggleSidebar());
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setLocalSearchQuery(query);
    onSearchChange?.(query);
  };

  const handleClearSearch = () => {
    setLocalSearchQuery('');
    onSearchChange?.('');
  };

  const handleClickOpen = () => {
    setPopup(true);
  };

  const handleClickClose = () => {
    setPopup(false);
  };

  const handleLogoutPopup = () => {
    handleClickOpen();
  };

  return (
    <SideBarRoot>
      <Drawer
        container={container}
        variant="temporary"
        open={sideBarOpen}
        onClose={handleDrawerToggle}
        // onClick={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: '18rem',
            paddingTop: '15px',
            '&::-webkit-scrollbar': {
              width: '0px',
            },
          },
        }}
      >
        <Stack className="brand-wrap" sx={{ height: '3rem', pl: '20px' }} direction="row" alignItems="center">
          <Box
            className="logo-icon me-2 rounded-circle"
            sx={{ width: '45px', height: '45px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            border="1px solid #f1f2f3"
          >
            <img src={selectedSchool?.schoolLogo} alt={selectedSchool?.schoolLogo} width="45px" height="45px" />
            {/* <img src={passdailyLogo} alt="passdailyLogo" width="45px" height="45px" /> */}
            {/* <img src={holyLogo} alt="holyLogo" width="45px" height="45px" /> */}
            {/* <img src={carmelLogo} alt="carmelLogo" width="45px" height="45px" /> */}
            {/* <img src={thereseLogo} alt="thereseLogo" width="45px" height="45px" /> */}
            {/* <img src={thomasLogo} alt="thomasLogo" width="45px" height="45px" /> */}
            {/* <img src={nirmalaLogo} alt="nirmalaLogo" width="45px" height="45px" /> */}
            {/* <img src={MIMLogo} alt="MIMLogo" width="45px" height="45px" /> */}
          </Box>
          <Typography fontWeight={600} fontSize={20}>
            {selectedSchool?.schoolName || 'Passdaily'}
            {/* Passdaily */}
            {/* Holy Angels */}
            {/* Carmel */}
            {/* St Therese */}
            {/* St Thomas */}
            {/* Nirmala */}
            {/* MIM School */}
          </Typography>
        </Stack>

        <Divider color="gray" className="my-2" />

        {/* Search Input */}
        <Box sx={{ px: 1, pb: 1 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search Navigation"
            value={localSearchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <FiSearch className="search-icon" />
                  {/* <SearchIcon fontSize="small" className="search-icon" /> */}
                </InputAdornment>
              ),
              endAdornment: localSearchQuery && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch} edge="end">
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiInputBase-input': {
                padding: '0.5rem 0rem', // adjust as needed
              },
              '& .MuiOutlinedInput-root': {
                backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                borderRadius: 5,
                fontSize: '0.875rem',
                '& fieldset': {
                  borderColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                },
                '&:hover fieldset': {},
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
              '.search-icon': { transform: 'scale(1)', transition: 'transform 0.3s' },
              '&:hover .search-icon': {
                transform: 'scale(1.1)',
                color: theme.palette.primary.main,
                transition: 'transform 0.3s',
              },
            }}
          />
        </Box>
        {/* <div>{children}</div> */}
        <Box
          onClick={handleDrawerToggle}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleDrawerToggle();
            }
          }}
          role="button"
          tabIndex={0}
          style={{ cursor: 'pointer' }}
          mb={selectedSchool?.schoolId === '1' ? 11 : 0}
        >
          {children}
        </Box>
        {selectedSchool?.schoolId === '1' && (
          <Box mx={2} position="relative">
            <div
              style={{
                position: 'fixed',
                bottom: '10px',
                transform: 'none',
                width: '265px',
                height: '50px',
                overflow: 'hidden',
                borderRadius: '15px',
                boxShadow: 'rgba(0, 0, 0, 0.25) 0px 4px 8px, rgba(0, 0, 0, 0.15) 0px -10px 25px inset',
                backdropFilter: 'blur(5px) saturate(180%)',
                WebkitBackdropFilter: 'blur(5px) saturate(180%)',
                zIndex: 9999,
                pointerEvents: 'auto',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                padding: '5px 10px',
              }}
            >
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ height: '100%' }}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar
                    src={user?.photo || undefined} // Fix: ensure it's undefined if null
                    alt={user ? getDiplayName(user) : 'Guest'} // Fix: provide fallback alt
                    sx={{
                      bgcolor: isLight ? theme.palette.primary.main : theme.palette.primary.main,
                      width: '35px',
                      height: '35px',
                      color: theme.palette.common.white,
                    }}
                  >
                    {user ? getInitials(user) : ''}
                  </Avatar>
                  <Typography
                    width={110}
                    variant="subtitle2"
                    noWrap
                    sx={{
                      color: theme.palette.primary.main,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {user ? getDiplayName(user) : 'Guest User'}
                  </Typography>
                </Stack>

                <IconButton aria-label="" onClick={handleLogoutPopup}>
                  <FiLogOut color={theme.palette.secondary.main} className=" fs-5" />
                </IconButton>

                {/* <Button
                variant="outlined"
                size="small"
                // onClick={handleLogout}
                sx={{
                  color: '#000',
                  borderColor: theme.palette.grey[300],
                  background: '#fff',
                  '&:hover': {
                    borderColor: '#fff',
                    background: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                Logout
              </Button> */}
              </Stack>
            </div>
          </Box>
        )}
      </Drawer>
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            // width: ` calc(1rem + ${SIDE_BAR_WIDTH}) `,
            width: SIDE_BAR_WIDTH,
            marginTop: `${topBarHeight}px`,
            height: `calc(100% - ${TOP_BAR_HEIGHT})`,
            pb: '15px',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          },
        }}
        open
      >
        {/* Search Input for Desktop */}
        <Box
          sx={{
            py: '15px',
            px: 1,
            position: 'fixed',
            width: '14.8rem',
            backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[800],
            zIndex: 1,
          }}
        >
          <TextField
            fullWidth
            size="small"
            placeholder="Search Navigation"
            value={localSearchQuery}
            // variant="standard"
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <FiSearch className="search-icon" />
                  {/* <SearchIcon className="search-icon" fontSize="small" /> */}
                </InputAdornment>
              ),
              endAdornment: localSearchQuery && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch} edge="end">
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiInputBase-input': {
                padding: '0.5rem 0rem', // adjust as needed
              },
              '& .MuiOutlinedInput-root': {
                backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                borderRadius: 5,
                fontSize: '0.875rem',
                '& fieldset': {
                  borderColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                },
                '&:hover fieldset': {
                  borderColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                },
                '&.Mui-focused fieldset': {
                  borderColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                },
                '&.Mui-focused .search-icon': {
                  transform: 'scale(1.1)',
                  color: theme.palette.primary.main,
                  transition: 'transform 0.3s, color 0.3s',
                },
              },
              '.search-icon': {
                transform: 'scale(1)',
                transition: 'transform 0.3s, color 0.3s',
                // color: theme.palette.grey[600],
              },
              '&:hover .search-icon': {
                transform: 'scale(1.1)',
                color: theme.palette.primary.main,
                transition: 'transform 0.3s, color 0.3s',
              },
            }}
          />
        </Box>
        <Box mt={10} mb={selectedSchool?.schoolId === '1' ? 9 : 0}>
          {children}
        </Box>
        {selectedSchool?.schoolId === '1' && (
          <Box mx={2} position="relative">
            <div
              style={{
                position: 'fixed',
                bottom: '10px',
                transform: 'none',
                width: '14%',
                height: '50px',
                overflow: 'hidden',
                borderRadius: '15px',
                boxShadow: 'rgba(0, 0, 0, 0.25) 0px 4px 8px, rgba(0, 0, 0, 0.15) 0px -10px 25px inset',
                backdropFilter: 'blur(5px) saturate(180%)',
                WebkitBackdropFilter: 'blur(5px) saturate(180%)',
                zIndex: 9999,
                pointerEvents: 'auto',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                padding: '5px 10px',
              }}
            >
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ height: '100%' }}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar
                    src={user?.photo || undefined} // Fix: ensure it's undefined if null
                    alt={user ? getDiplayName(user) : 'Guest'} // Fix: provide fallback alt
                    sx={{
                      bgcolor: isLight ? theme.palette.primary.main : theme.palette.primary.main,
                      width: '35px',
                      height: '35px',
                      color: theme.palette.common.white,
                    }}
                  >
                    {user ? getInitials(user) : ''}
                  </Avatar>
                  <Typography
                    width={110}
                    variant="subtitle2"
                    noWrap
                    sx={{
                      color: theme.palette.primary.main,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {user ? getDiplayName(user) : 'Guest User'}
                  </Typography>
                </Stack>

                <IconButton aria-label="" onClick={handleLogoutPopup}>
                  <FiLogOut color={theme.palette.secondary.main} className=" fs-5" />
                </IconButton>

                {/* <Button
                variant="outlined"
                size="small"
                // onClick={handleLogout}
                sx={{
                  color: '#000',
                  borderColor: theme.palette.grey[300],
                  background: '#fff',
                  '&:hover': {
                    borderColor: '#fff',
                    background: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                Logout
              </Button> */}
              </Stack>
            </div>
          </Box>
        )}
      </Drawer>
      <LogoutBox popup={popup} handleClickOpen={handleClickOpen} handleClickClose={handleClickClose} />
    </SideBarRoot>
  );
});

export default SideBar;
