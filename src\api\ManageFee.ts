import ApiUrls from '@/config/ApiUrls';
import {
  ClassListDataType,
  ClassSectionsDataType,
  CreateBasicFeeSettingDataType,
  CreateScholarshipFeeSettingDataType,
  CreateScholarshipSettingsDataType,
  CreateTermFeeSettingDataType,
  CreateTermFeeSettingTitleDataType,
  FeeDateSettingsType,
  ScholarshipFeeListType,
  GetOptionalFeeSettingsDataType,
  GetIndividualFeeSettingsDataType,
  GetScholarshipSettingsDataType,
  OptionalFeeSettingsDataType,
  IndividualFeeSettingsDataType,
  StudentFeeStatusDataType,
  StudentTermFeePayType,
  StudentTermFeeStatusDataType,
  StudentTermFeeStatusNewType,
  TermFeeDetailsDataType,
  CreateBasicFeeSettingTitleDataType,
  DeleteScholarshipType,
  DeleteTermFeeListType,
  UpdateTermFeeListType,
  DeleteBasicFeeListType,
  UpdateBasicFeeListType,
  GetFeeOverviewStatusType,
  GetFeeOverviewChartType,
  GetFeeOverviewPaidListType,
  GetFeeOverviewModeListType,
  GetReceiptForPrintType,
  GetFeePaidListType,
  GetStudentsFilterDataType,
  GetFeePaidBasicListRequestType,
  GetBasicFeeFilterDataType,
  GetFeePaidTermListRequestType,
  GetTermFeeFilterDataType,
  GetFeePendingListRequestType,
  GetFeePendingBasicListRequestType,
  GetFeePendingTermListRequestType,
  GetFineListRequestType,
  GetFineListDataType,
  CreateEditFineListRequestType,
  CreateEditFineListResponseType,
  DeleteFineListRequestType,
  DeleteFineListResponseType,
  GetFineMappingListDataType,
  FineMapInsertDataType,
  FineMapDeleteDataType,
  BasicFeeMappedDeleteDataType,
  BasicFeeMappedDeleteAllDataType,
  TermFeeMappedDeleteDataType,
  TermFeeMappedDeleteAllDataType,
  GetBasicFeeListRequestType,
  GetTermFeeListType,
  ReceiptCancelRequestType,
  CheckReceiptNoType,
  OptionalFeeMappedDeleteType,
  OptionalFeeMappedDeleteAllType,
  ScholarshipMappedDeleteType,
  ScholarshipMappedDeleteAllType,
  StopMappingSettingsType,
  CreateStopMappingType,
  DeleteAllBusMappedStudentType,
  DeleteBusMappedType,
  ReceiptApproveRequestType,
  FeeOverviewPaidListRequest,
  GetStudentPickerDataType,
  StudentPickerRequest,
  TakeFeePaidDailyListRequest,
  TakeFeePaidDailyListResponse,
  StudentPickerNewRequest,
  StudentPickerNewResponse,
} from '@/types/ManageFee';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from './base/api';
import { APIResponse } from './base/types';

// async function GetTermFee(request: TermFeeRequest): Promise<APIResponse<TermFeeDetailsDataType[]>> {
//   const response = await privateApi.post<TermFeeDetailsDataType[]>(ApiUrls.GetTermFee, request);
//   console.log('response', response);
//   return response;
// }

async function GetTermFee(
  adminId: number | undefined,
  academicId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<TermFeeDetailsDataType[]>> {
  if (adminId === undefined || academicId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetTermFee.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetClassSections(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<ClassSectionsDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetClassSections.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetFeeDateSettings(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<FeeDateSettingsType[]>> {
  if (adminId === undefined || academicId === undefined || sectionId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetFeeDateSettings.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetStudentTermFeeStatus(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  studentId: number | undefined
): Promise<APIResponse<StudentTermFeeStatusDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    studentId === undefined
  ) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetStudentTermFeeStatus.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{studentId}', studentId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetClassListData(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined
): Promise<APIResponse<ClassListDataType[]>> {
  if (adminId === undefined || academicId === undefined || sectionId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetClassListData.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetStudentsFeeStatus(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<StudentFeeStatusDataType[]>> {
  if (adminId === undefined || academicId === undefined || sectionId === undefined || classId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetStudentsFeeStatus.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetStudentTermFeeStatusNew(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  studentId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<StudentTermFeeStatusNewType>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    studentId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId, academicId, sectionId, classId , studentId and feeTypeId are required');
  }

  const url = ApiUrls.GetStudentTermFeeStatusNew.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{studentId}', studentId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function StudentTermFeePay(request: StudentTermFeePayType): Promise<APIResponse<StudentTermFeePayType>> {
  const response = await privateApi.post<StudentTermFeePayType>(ApiUrls.StudentTermFeePay, request);
  return response;
}
// Create Basic Fee Setting
async function CreateBasicFeeSetting(request: CreateBasicFeeSettingDataType[]): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateBasicFeeSetting, request);
  return response;
}

// Create Term Fee Setting
async function CreateTermFeeSetting(request: CreateTermFeeSettingDataType): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateTermFeeSetting, request);
  return response;
}

// Create Term Fee Setting Title
async function CreateBasicFeeSettingTitle(
  request: CreateBasicFeeSettingTitleDataType
): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateBasicFeeSettingTitle, request);
  return response;
}

// Create Term Fee Setting Title
async function CreateTermFeeSettingTitle(
  request: CreateTermFeeSettingTitleDataType
): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateTermFeeSettingTitle, request);
  return response;
}
// Get Optional Fee Settings

async function GetOptionalFeeSettings(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetOptionalFeeSettingsDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId , academicId ,sectionId,classId and feeTypeId are required');
  }

  const url = ApiUrls.GetOptionalFeeSettings.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Optional Fee Settings
async function OptionalFeeSettings(request: OptionalFeeSettingsDataType): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.OptionalFeeSettings, request);
  return response;
}

// Get Optional Fee Settings Individual
async function GetOptionalFeeSettingsIndividual(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetIndividualFeeSettingsDataType>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId , academicId ,sectionId,classId and feeTypeId are required');
  }

  const url = ApiUrls.GetOptionalFeeSettingsIndividual.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Optional Fee Settings Individual
async function OptionalFeeSettingsIndividual(request: IndividualFeeSettingsDataType): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.OptionalFeeSettingsIndividual, request);
  return response;
}

// Get Scholarship Settings

async function GetScholarshipSettings(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetScholarshipSettingsDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId , academicId , sectionId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetScholarshipSettings.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
// Create Scholarship Setting
async function CreateScholarshipFeeSetting(
  request: CreateScholarshipFeeSettingDataType
): Promise<APIResponse<CreateScholarshipFeeSettingDataType>> {
  const response = await privateApi.post<CreateScholarshipFeeSettingDataType>(
    ApiUrls.CreateScholarshipFeeSetting,
    request
  );
  return response;
}
// Get Scholarship List
async function GetScholarshipFeeList(
  adminId: number | undefined,
  academicId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<ScholarshipFeeListType>> {
  if (adminId === undefined || academicId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetScholarshipFeeList.replace('{adminId}', adminId.toString())
    .replace('{accademicId}', academicId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Scholarship Settings
async function CreateScholarshipSettings(
  request: CreateScholarshipSettingsDataType
): Promise<APIResponse<CreateScholarshipSettingsDataType>> {
  const response = await privateApi.post<CreateScholarshipSettingsDataType>(ApiUrls.CreateScholarshipSettings, request);
  return response;
}
// Delete Scholarship
async function DeleteScholarship(request: DeleteScholarshipType[]): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteScholarship, request);
  return response;
}

// Get Term Fee List
async function GetTermFeeList(request: GetTermFeeListType): Promise<APIResponse<CreateTermFeeSettingTitleDataType[]>> {
  const response = await privateApi.post(ApiUrls.GetTermFeeList, request);
  return response;
}

// Delete Term Fee List
async function DeleteTermFeeList(request: DeleteTermFeeListType[]): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteTermFeeList, request);
  return response;
}
// Update Term Fee List
async function UpdateTermFeeList(request: UpdateTermFeeListType): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateTermFeeList, request);
  return response;
}

// Get Basic Fee List
async function GetBasicFeeList(
  request: GetBasicFeeListRequestType
): Promise<APIResponse<CreateBasicFeeSettingTitleDataType[]>> {
  const response = await privateApi.post(ApiUrls.GetBasicFeeList, request);
  return response;
}

// Delete Basic Fee List
async function DeleteBasicFeeList(request: DeleteBasicFeeListType[]): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteBasicFeeList, request);
  return response;
}
// Update Basic Fee List
async function UpdateBasicFeeList(request: UpdateBasicFeeListType): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateBasicFeeList, request);
  return response;
}

// ------ Manage Fee OverView ------

// Get Fee Overview Status
async function GetFeeOverviewStatus(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetFeeOverviewStatusType>> {
  if (adminId === undefined || academicId === undefined || classId === undefined || feeTypeId === undefined) {
    throw new Error('adminId ,academicId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetFeeOverviewStatus.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Get Fee Overview Chart
async function GetFeeOverviewChart(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetFeeOverviewChartType[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined || feeTypeId === undefined) {
    throw new Error('adminId ,academicId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetFeeOverviewChart.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetFeeOverviewPaidList(
  request: FeeOverviewPaidListRequest
): Promise<APIResponse<GetFeeOverviewPaidListType[]>> {
  const response = await privateApi.post<GetFeeOverviewPaidListType[]>(ApiUrls.GetFeeOverviewPaidList, request);
  return response;
}

// Get Fee Overview Mode List
async function GetFeeOverviewModeList(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetFeeOverviewModeListType[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined || feeTypeId === undefined) {
    throw new Error('adminId ,academicId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetFeeOverviewModeList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
// -------- Receipt Print --------------- //
// Get Receipt For Print
async function GetReceiptForPrint(
  adminId: number | undefined,
  receiptId: number | undefined
): Promise<APIResponse<GetReceiptForPrintType[]>> {
  if (adminId === undefined || receiptId === undefined) {
    throw new Error('adminId and receiptId are required');
  }

  const url = ApiUrls.GetReceiptForPrint.replace('{adminId}', adminId.toString()).replace(
    '{receiptId}',
    receiptId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
// -----------------------------------------
// Get Fee Paid List
async function GetFeePaidList(request: GetFeePaidListType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePaidList, request);
  return response;
}

// Get Students Filter
async function GetStudentsFilter(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetStudentsFilterDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId ,academicId ,sectionId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetStudentsFilter.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
// -----------------------------------------

// -----------------------------------------
// Get Fee Paid Basic List
async function GetFeePaidBasicList(request: GetFeePaidBasicListRequestType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePaidBasicList, request);
  return response;
}

// Get Basic Fee Filter
async function GetBasicFeeFilter(
  adminId: number | undefined,
  academicId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetBasicFeeFilterDataType[]>> {
  if (adminId === undefined || academicId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetBasicFeeFilter.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
// -----------------------------------------

// -----------------------------------------
// Get Fee Paid Term List
async function GetFeePaidTermList(request: GetFeePaidTermListRequestType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePaidTermList, request);
  return response;
}

// Get Term Fee Filter
async function GetTermFeeFilter(
  adminId: number | undefined,
  academicId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetTermFeeFilterDataType[]>> {
  if (adminId === undefined || academicId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetTermFeeFilter.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
// -----------------------------------------

// Get Fee Pending List
async function GetFeePendingList(request: GetFeePendingListRequestType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePendingList, request);
  return response;
}

// Get Fee Pending Basic List
async function GetFeePendingBasicList(request: GetFeePendingBasicListRequestType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePendingBasicList, request);
  return response;
}

// Get Fee Pending Term List
async function GetFeePendingTermList(request: GetFeePendingTermListRequestType): Promise<APIResponse<any>> {
  const response = await privateApi.post(ApiUrls.GetFeePendingTermList, request);
  return response;
}

// Get Fine List
async function GetFineList(request: GetFineListRequestType): Promise<APIResponse<GetFineListDataType[]>> {
  const response = await privateApi.post(ApiUrls.GetFineList, request);
  return response;
}
// CreateEdit Fine List
async function CreateEditFineList(
  request: CreateEditFineListRequestType
): Promise<APIResponse<CreateEditFineListResponseType>> {
  const response = await privateApi.post(ApiUrls.CreateEditFineList, request);
  return response;
}
// Delete Fine List
async function DeleteFineList(request: DeleteFineListRequestType): Promise<APIResponse<DeleteFineListResponseType>> {
  const response = await privateApi.post(ApiUrls.DeleteFineList, request);
  return response;
}

// Get Fine Mapping List
async function GetFineMappingList(
  adminId: number | undefined,
  academicId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetFineMappingListDataType[]>> {
  if (adminId === undefined || academicId === undefined || feeTypeId === undefined) {
    throw new Error('adminId , academicId and feeTypeId are required');
  }

  const url = ApiUrls.GetFineMappingList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Fine Map Insert
async function FineMapInsert(request: FineMapInsertDataType): Promise<APIResponse<FineMapInsertDataType>> {
  const response = await privateApi.post<FineMapInsertDataType>(ApiUrls.FineMapInsert, request);
  return response;
}

// Delete Fine Map
async function DeleteFineMap(request: FineMapDeleteDataType[]): Promise<APIResponse<FineMapDeleteDataType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteFineMap, request);
  return response;
}

// Delete Basic Fee Mapped
async function DeleteBasicFee(
  request: BasicFeeMappedDeleteDataType[]
): Promise<APIResponse<BasicFeeMappedDeleteDataType>> {
  const response = await privateApi.post<BasicFeeMappedDeleteDataType>(ApiUrls.DeleteBasicFee, request);
  return response;
}
// async function DeleteBasicFee(
//   request: BasicFeeMappedDeleteDataType[]
// ): Promise<APIResponse<BasicFeeMappedDeleteDataType[]>> {
//   const response = await privateApi.post(ApiUrls.DeleteBasicFee, request);
//   return response;
// }

// Delete All Basic Fee Mapped
async function DeleteAllBasicFee(request: BasicFeeMappedDeleteAllDataType[]): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteAllBasicFee, request);
  return response;
}
// async function DeleteAllBasicFee(
//   request: BasicFeeMappedDeleteAllDataType[]
// ): Promise<APIResponse<BasicFeeMappedDeleteAllDataType[]>> {
//   const response = await privateApi.post(ApiUrls.DeleteAllBasicFee, request);
//   return response;
// }

// Delete Term Fee Mapped
async function DeleteTermFee(
  request: TermFeeMappedDeleteDataType[]
): Promise<APIResponse<TermFeeMappedDeleteDataType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteTermFee, request);
  return response;
}

// Delete All Term Fee Mapped
async function DeleteAllTermFee(
  request: TermFeeMappedDeleteAllDataType[]
): Promise<APIResponse<TermFeeMappedDeleteAllDataType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteAllTermFee, request);
  return response;
}
// Receipt Cancel
async function ReceiptCancel(request: ReceiptCancelRequestType): Promise<APIResponse<ReceiptCancelRequestType>> {
  const response = await privateApi.post(ApiUrls.ReceiptCancel, request);
  return response;
}

// Check Receipt No
async function CheckReceiptNo(
  receiptNo: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<CheckReceiptNoType>> {
  if (receiptNo === undefined || feeTypeId === undefined) {
    throw new Error('receiptNo and feeTypeId are required');
  }

  const url = ApiUrls.CheckReceiptNo.replace('{receiptNo}', receiptNo.toString()).replace(
    '{feeTypeId}',
    feeTypeId.toString()
  );
  console.log('responsereceiptNo::::----', receiptNo);

  const response = await privateApi.get<any>(url);
  console.log('response1212::::----', response);

  return response;
}

// Delete Optional Fee Mapped
async function DeleteOptionalFee(
  request: OptionalFeeMappedDeleteType[]
): Promise<APIResponse<OptionalFeeMappedDeleteType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteOptionalFee, request);
  return response;
}

// Delete All Term Fee Mapped
async function DeleteAllOptionalFee(
  request: OptionalFeeMappedDeleteAllType[]
): Promise<APIResponse<OptionalFeeMappedDeleteAllType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteAllOptionalFee, request);
  return response;
}

// Delete Scholarship Mapped
async function DeleteScholarshipMapped(
  request: ScholarshipMappedDeleteType[]
): Promise<APIResponse<ScholarshipMappedDeleteType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteScholarshipMapped, request);
  return response;
}

// Delete All Scholarship Mapped
async function DeleteAllScholarshipMapped(
  request: ScholarshipMappedDeleteAllType[]
): Promise<APIResponse<ScholarshipMappedDeleteAllType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteAllScholarshipMapped, request);
  return response;
}

// Get Stop Mapping Settings
async function GetStopMappingSettings(
  adminId: number | undefined,
  academicId: number | undefined,
  sectionId: number | undefined,
  classId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<StopMappingSettingsType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    sectionId === undefined ||
    classId === undefined ||
    feeTypeId === undefined
  ) {
    throw new Error('adminId , academicId , sectionId , classId and feeTypeId are required');
  }

  const url = ApiUrls.GetStopMappingSettings.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{sectionId}', sectionId.toString())
    .replace('{classId}', classId.toString())
    .replace('{feeTypeId}', feeTypeId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

// Create Stop Mapping
async function CreateStopMapping(request: CreateStopMappingType[]): Promise<APIResponse<CreateStopMappingType[]>> {
  const response = await privateApi.post(ApiUrls.CreateStopMapping, request);
  return response;
}

// Delete Bus Mapped
async function DeleteBusMapped(request: DeleteBusMappedType[]): Promise<APIResponse<DeleteBusMappedType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteBusMapped, request);
  return response;
}

// Delete All Bus Mapped Student
async function DeleteAllBusMappedStudent(
  request: DeleteAllBusMappedStudentType[]
): Promise<APIResponse<DeleteAllBusMappedStudentType[]>> {
  const response = await privateApi.post(ApiUrls.DeleteAllBusMappedStudent, request);
  return response;
}

// Receipt Cancel
async function ReceiptApprove(request: ReceiptApproveRequestType): Promise<APIResponse<ReceiptApproveRequestType>> {
  const response = await privateApi.post(ApiUrls.ReceiptApprove, request);
  return response;
}

async function GetStudentPickerData(request: StudentPickerRequest): Promise<APIResponse<GetStudentPickerDataType[]>> {
  const response = await privateApi.post<GetStudentPickerDataType[]>(ApiUrls.GetStudentPickerData, request);
  return response;
}

// Daily Fee Payment List
async function TakeFeePaidDailyList(request: TakeFeePaidDailyListRequest): Promise<APIResponse<TakeFeePaidDailyListResponse[]>> {
  const response = await privateApi.post<TakeFeePaidDailyListResponse[]>(ApiUrls.TakeFeePaidDailyList, request);
  return response;
}

// Student Picker New Version
async function StudentPickerNew(request: StudentPickerNewRequest): Promise<APIResponse<StudentPickerNewResponse[]>> {
  const response = await privateApi.post<StudentPickerNewResponse[]>(ApiUrls.StudentPickerNew, request);
  return response;
}

const methods = {
  GetTermFee,
  GetClassSections,
  GetFeeDateSettings,
  GetStudentTermFeeStatus,
  GetClassListData,
  GetStudentsFeeStatus,
  StudentTermFeePay,
  GetStudentTermFeeStatusNew,
  CreateBasicFeeSetting,
  CreateTermFeeSetting,
  CreateBasicFeeSettingTitle,
  CreateTermFeeSettingTitle,
  GetOptionalFeeSettings,
  OptionalFeeSettings,
  GetOptionalFeeSettingsIndividual,
  OptionalFeeSettingsIndividual,
  GetScholarshipFeeList,
  CreateScholarshipFeeSetting,
  GetScholarshipSettings,
  CreateScholarshipSettings,
  DeleteScholarship,
  GetTermFeeList,
  DeleteTermFeeList,
  UpdateTermFeeList,
  UpdateBasicFeeList,
  DeleteBasicFeeList,
  GetBasicFeeList,
  GetFeeOverviewStatus,
  GetFeeOverviewChart,
  GetFeeOverviewPaidList,
  GetFeeOverviewModeList,
  GetReceiptForPrint,
  GetFeePaidList,
  GetStudentsFilter,
  GetFeePaidBasicList,
  GetBasicFeeFilter,
  GetFeePaidTermList,
  GetTermFeeFilter,
  GetFeePendingList,
  GetFeePendingBasicList,
  GetFeePendingTermList,
  GetFineList,
  CreateEditFineList,
  DeleteFineList,
  GetFineMappingList,
  FineMapInsert,
  DeleteFineMap,
  DeleteBasicFee,
  DeleteAllBasicFee,
  DeleteTermFee,
  DeleteAllTermFee,
  ReceiptCancel,
  CheckReceiptNo,
  DeleteOptionalFee,
  DeleteAllOptionalFee,
  DeleteScholarshipMapped,
  DeleteAllScholarshipMapped,
  GetStopMappingSettings,
  CreateStopMapping,
  DeleteBusMapped,
  DeleteAllBusMappedStudent,
  ReceiptApprove,
  GetStudentPickerData,
  TakeFeePaidDailyList,
  StudentPickerNew,
};

export default methods;
