/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, ReactElement, useCallback, useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  RadioGroup,
  Select,
  MenuItem,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Radio,
  FormControlLabel,
  SelectChangeEvent,
  FormControl,
  IconButton,
  Checkbox,
  Tooltip,
  Collapse,
  Chip,
  Alert,
  Snackbar,
  FormHelperText,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import NoData from '@/assets/no-datas.png';
import AddIcon from '@mui/icons-material/Add';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import EditIcon from '@mui/icons-material/Edit';
import LoadingButton from '@mui/lab/LoadingButton';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import useAuth from '@/hooks/useAuth';
import { CreateScholarshipFeeSettingDataType, DeleteScholarshipType, ScholarshipDetailsType } from '@/types/ManageFee';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getYearData } from '@/config/storeSelectors';
import {
  DeleteScholarship,
  createScholarshipFeeSetting,
  fetchScholarshipFeeList,
} from '@/store/ManageFee/manageFee.thunks';
import DeleteIcon from '@mui/icons-material/Delete';
import Person2Icon from '@mui/icons-material/Person2';
import SearchIcon from '@mui/icons-material/Search';
import SearchOffIcon from '@mui/icons-material/SearchOff';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import paidSuccess from '@/assets/ManageFee/paidSuccess.json';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import * as Yup from 'yup';
import { Formik } from 'formik';
import FeeAllocation from './FeeAllocation';
import ScholarshipSetting from '../Unused/ScholarshipSetting';
import FeeAllocation2 from './FeeAllocation2';

const ScholarshipRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export type ScholarshipAllocationType = {
  id?: number;
  feeId: number;
  termId: number;
  type: number;
  value: string;
  dbResult: string;
  scholarshipId: number;
  scholarshipMapId: number;
};

// export type ScholarshipDetailsType = {
//   id: number;
//   title: string;
//   type: number;
//   description: string;
//   adminId: number;
//   dbResult: string;
//   scholarshipId: number;
// };

const validationSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  type: Yup.number().oneOf([1, 2], 'Type is required'),
  description: Yup.string().required('Description is required'),
});

export default function Scholarship() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const YearData = useAppSelector(getYearData);
  const { confirm } = useConfirm();
  const [popup, setPopup] = useState({ id: 0, open: false });
  const [popup2, setPopup2] = useState({ id: 0, row: {}, open: false });
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(true);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: number }>({});
  const [individualRemoveRowButton, setIndividualRemoveRowButton] = useState<{ [key: string]: number }>({});
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [rowBackgroundColors, setRowBackgroundColors] = useState<Record<number, string>>({});
  const [editTableRow, setEditTableRow] = useState<Record<number, boolean>>({});
  const [rowSelect, setRowSelect] = useState<Record<number, boolean>>({});
  const [id, setId] = useState();
  const [showFeeAllocationView, setShowFeeAllocationView] = useState<'EditView' | 'TableView'>('TableView');
  const [mapToStudent, setMapToStudent] = useState<'sholarship' | 'mapToStudent'>('sholarship');
  const [scholarshipValues, setScholarshipValues] = useState<ScholarshipAllocationType[]>([
    { id: 0, feeId: -1, termId: -1, type: 1, value: '', dbResult: '', scholarshipId: 0, scholarshipMapId: 0 },
  ]);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);

  const handleClose = () =>
    setPopup((row) => {
      return { ...row, open: false };
    });
  const [scholarshipValuesArray, setScholarshipValuesArray] = useState<ScholarshipAllocationType[]>([]);

  // const [scholarshipValues2, setScholarshipValues2] = useState<any[]>([]);
  const [scholarshipValues2, setScholarshipValues2] = useState<ScholarshipAllocationType[]>([
    { id: 0, feeId: -1, termId: -1, type: 1, value: '', dbResult: '', scholarshipId: 0, scholarshipMapId: 0 },
  ]);
  const handleClose2 = () => {
    setPopup2((prev) => {
      return { ...prev, open: false };
    });
  };
  // const handleOpenPopup = (row, scholarshipId) => {
  //   setPopup2(true);
  //   setScholarshipValues2(row);
  //   setId(scholarshipId);
  //   console.log('row::::----', row);
  // };
  const handleSave2 = (updatedValues: any) => {
    setScholarshipValuesArray(updatedValues);
    console.log('scholarshipValuesArray:::-', scholarshipValuesArray);
  };

  useEffect(() => {
    // Load scholarshipValuesArray from local storage on component mount
    const storedScholarshipValuesArray = localStorage.getItem('scholarshipValuesArray');
    if (storedScholarshipValuesArray) {
      setScholarshipValuesArray(JSON.parse(storedScholarshipValuesArray));
    }
    console.log('storedScholarshipValuesArray:::-', storedScholarshipValuesArray);
  }, []);
  const [scholarshipList, setScholarshipList] = useState<ScholarshipDetailsType[]>([
    {
      id: 0,
      scholarshipId: 0,
      title: '',
      type: -1,
      description: '',
      status: 0,
      scholarshipMapped: {
        scholarshipMapId: 0,
        feeId: 0,
        termId: 0,
        type: 0,
        value: 0,
      },
    },
  ]);
  const handleAddRow = () => {
    const newRow: ScholarshipDetailsType = {
      id: scholarshipList.length + 1,
      scholarshipId: 0,
      title: '',
      type: -1,
      description: '',
      status: 1,
      scholarshipMapped: {
        scholarshipMapId: 0,
        feeId: 0,
        termId: 0,
        type: 0,
        value: 0,
      },
    };
    setScholarshipList([...scholarshipList, newRow]);
    setRowBackgroundColors((prev) => ({
      ...prev,
      [newRow.scholarshipId]: isLight ? '#f0fdf4' : theme.palette.grey[900],
    }));
    setIndividualSaveButtonEnabled((prev) => ({ ...prev, [newRow.scholarshipId]: true }));
    setIndividualRemoveRowButton((prev) => ({ ...prev, [newRow.scholarshipId]: true }));
    setEditTableRow((prev) => ({ ...prev, [newRow.scholarshipId]: true }));
  };

  const handleTitleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: ScholarshipDetailsType
  ) => {
    setScholarshipList((prevRows) =>
      prevRows.map((row) => {
        if (row.id === item.id) {
          return { ...row, title: event.target.value };
        }
        return row;
      })
    );
  };
  const handleDescriptionChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: ScholarshipDetailsType
  ) => {
    const { value } = event.target; // Extract the value from the event
    setScholarshipList((prevRows) =>
      prevRows.map((row) => {
        if (row.id === item.id) {
          return { ...row, description: value }; // Update description with the extracted value
        }
        console.log('value::::----', value);
        return row;
      })
    );
  };
  // Handler to update the state when radio button value changes
  const handleStatusChange = (event: React.ChangeEvent<HTMLInputElement>, item: ScholarshipDetailsType) => {
    const { value } = event.target;
    setScholarshipList((prevRows) =>
      prevRows.map((row) => {
        if (row.id === item.id) {
          return { ...row, status: Number(value) }; // Update status with the new value
        }
        return row;
      })
    );
  };

  const handleDelete = (row: ScholarshipDetailsType) => {
    setScholarshipList((prev) => prev.filter((val) => !(val.id === row.id)));
    setScholarshipValues((prev) => prev.filter((val) => !(val.id === row.id)));
  };

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  const initialScholarshipListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentScholarshipListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );

  const loadScholarshipList = useCallback(
    async (request: { adminId: number; academicId: number; feeTypeId: number }) => {
      try {
        const data: any = await dispatch(fetchScholarshipFeeList(request)).unwrap();
        if (data) {
          const scholarship = data.scholarshipList
            ? data.scholarshipList.map((item: ScholarshipDetailsType) => ({ ...item }))
            : [];
          setScholarshipList(scholarship);
          console.log('data::::----', data);
        }
      } catch (error) {
        console.error('Error loading Scholarship list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadScholarshipList(initialScholarshipListRequest);
    console.log('scholarshipValues2::::----', scholarshipValues2);
  }, [dispatch, adminId, scholarshipValues2, initialScholarshipListRequest, loadScholarshipList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadScholarshipList({ ...currentScholarshipListRequest, academicId: parseInt(e.target.value, 10) });
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadScholarshipList({ ...currentScholarshipListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleSave = useCallback(
    async (values: any, row: ScholarshipDetailsType) => {
      try {
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.scholarshipId]: true }));
        const scholarship = scholarshipValues.filter((val) => val.scholarshipId === row.scholarshipId);
        console.log('scholarship:::-', scholarship);
        console.log('scholarshipValuesArray:::-', scholarshipValuesArray);
        const sendreq: CreateScholarshipFeeSettingDataType = [
          {
            adminId,
            accademicId: academicYearFilter,
            scholarshipId: row.scholarshipId,
            title: row.title,
            description: row.description,
            type: row.type,
            feeTypeId: feeTypeFilter,
            dbResult: row.dbResult,
            scholarshipValues: scholarshipValuesArray.map((item) => {
              const { value, feeId, termId, type } = item;
              const val = parseInt(value, 10);
              const typeValue = type;
              return { feeId, termId, type: typeValue, value: val, dbResult: '', scholarshipMapId: 0 };
            }),
          },
        ];
        console.log('sendreq::::----', sendreq);
        const response = await dispatch(createScholarshipFeeSetting(sendreq)).unwrap();

        if (response) {
          console.log('response::::----', response);
          // setScholarshipValuesArray([]);
          // Reset scholarshipValues for this scholarshipId
          setScholarshipValuesArray((prev) => prev.filter((item) => item.scholarshipId !== row.scholarshipId));
          if (response.find((i) => i.dbResult === 'AlreadyExist')) {
            setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.scholarshipId]: false }));
            loadScholarshipList(initialScholarshipListRequest);
            setRowSelect((prev) => ({ ...prev, [row.scholarshipId]: false }));
            await showConfirmation(
              <ErrorMessage loop={false} jsonIcon={errorIcon} message="Scholarship list already exist" />,
              ''
            );
          } else {
            setScholarshipValuesArray([]);
            loadScholarshipList(initialScholarshipListRequest);
            setIndividualSaveButtonEnabled((prev) => ({ ...prev, [row.scholarshipId]: false }));
            setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.scholarshipId]: false }));
            console.log('response::::----', response);
            await showConfirmation(
              <SuccessMessage loop={false} jsonIcon={paidSuccess} message="Added successfully" />,
              ''
            );
            setScholarshipList((prevList) =>
              prevList.map((item) => (item.scholarshipId === row.scholarshipId ? { ...item, ...row } : item))
            );
            setRowBackgroundColors((prev) => ({
              ...prev,
              [row.scholarshipId]: isLight ? theme.palette.common.white : theme.palette.grey[700],
            }));
          }
        }
      } catch (error) {
        await showConfirmation(<ErrorMessage icon="" message="Failed. Please try again." />, '');
        console.error(error);
        setIndividualSaveButtonEnabled((prev) => ({ ...prev, [row.scholarshipId]: false }));
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.scholarshipId]: false }));
      }
    },
    [
      academicYearFilter,
      feeTypeFilter,
      adminId,
      dispatch,
      scholarshipValues,
      showConfirmation,
      loadScholarshipList,
      initialScholarshipListRequest,
      scholarshipValuesArray,
      isLight,
      theme,
    ]
  );

  const handleDeleteRow = useCallback(
    async (row: ScholarshipDetailsType) => {
      const { scholarshipId } = row;
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={<div style={{ color: theme.palette.error.main }}>Are you sure you want to delete the row ?</div>}
        />
      );
      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, accademicId: academicYearFilter, scholarshipId, dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteScholarship(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteScholarshipType[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          setSnackBar(true);
          setScholarshipList((prevDetails) => prevDetails.filter((item) => item.scholarshipId !== scholarshipId));
          // setSelectedCards((prevSelectedCards) =>
          //   prevSelectedCards.filter((card) => card.messageId !== errorMessages?.messageId)
          // );
          // Filter out the deleted cards from messageTempListDatas
          // setMessageTempListDatas((prevMessageTempListDatas) =>
          //   prevMessageTempListDatas.filter((item: any) => !selectedCards.includes(item.messageId))
          // );
          // setLoadRefresh(false);
          // loadMessageTempList(currentMessageTempRequest);

          // setSelectedCards([]);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme]
  );

  const handleDeleteMultiple = useCallback(async () => {
    try {
      const sendConfirmMessage = (
        <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
      );
      if (await confirm(sendConfirmMessage, 'Delete Scholarhip?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
        const sendRequests: any = await Promise.all(
          Object.keys(rowSelect)
            .map(async (key: any) => {
              if (rowSelect[key]) {
                return {
                  adminId,
                  accademicId: academicYearFilter,
                  scholarshipId: key, // use the key as termId
                  dbResult: '',
                  deletedId: 0,
                };
              }
              return null;
            })
            .filter((request) => request !== null)
        );
        const deleteResponse = await dispatch(DeleteScholarship(sendRequests));

        console.log('deleteResponse', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteScholarshipType[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            loadScholarshipList(initialScholarshipListRequest);
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            setRowSelect({});
            setIsAllSelected(false);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = (
              <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
            );
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }

          // setTermFeeData((prevDetails) =>
          //   prevDetails.filter((item: CreateTermFeeSettingTitleDataType) => !selectedRows.includes(item.termId))
          // );
        }
      }
    } catch {
      const deleteErrorMessage = (
        <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
      );
      await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
    }
  }, [confirm, dispatch, rowSelect, adminId, academicYearFilter, loadScholarshipList, initialScholarshipListRequest]);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setFeeTypeFilter(0);
      loadScholarshipList(initialScholarshipListRequest);
    },
    [initialScholarshipListRequest, loadScholarshipList]
  );

  const handleEditCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>, scholarshipId: number) => {
    const { checked } = event.target;

    if (checked) {
      setRowSelect((prev) => ({ ...prev, [scholarshipId]: true }));
      console.log('rowSelect::::----', rowSelect);
    } else {
      setRowSelect((prev) => ({ ...prev, [scholarshipId]: false }));
    }
  };

  const handleSelectAll = (event: ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setIsAllSelected(isChecked);

    const selectedRows = isChecked
      ? scholarshipList
          .filter((item) => item.scholarshipId) // only include rows with scholarshipId
          .reduce((acc, item) => {
            acc[item.scholarshipId] = true;
            return acc;
          }, {} as Record<string, boolean>)
      : {};

    setRowSelect(selectedRows);
  };

  const action = (
    <Button color="secondary" size="small">
      UNDO
    </Button>
  );
  // Check if there is any scholarship with a different scholarshipId in the scholarshipList
  return mapToStudent === 'sholarship' ? (
    <Page title="Scholarship">
      <ScholarshipRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between" flexWrap="wrap" gap={1}>
            <Typography variant="h6" fontSize={17}>
              Scholarship / Discount Type Creation
            </Typography>
            <Stack direction="row" justifyContent="end" flex={1}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <SearchOffIcon />
                </IconButton>
              )}
            </Stack>
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="end"
              sx={{
                '@media (max-width:630px)': {
                  flex: 1,
                  paddingBottom: 1,
                },
              }}
            >
              {scholarshipList.length > 0 && Object.values(rowSelect).some((checked) => checked) && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Stack>
                <Button
                  type="button"
                  color="primary"
                  variant="contained"
                  startIcon={<Person2Icon />}
                  sx={{ whiteSpace: 'nowrap' }}
                  onClick={() => setMapToStudent('mapToStudent')}
                >
                  Map Student
                </Button>
              </Stack>
            </Stack>
          </Box>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={1} container columnSpacing={2} rowSpacing={0.5} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} md={4} sm={4} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px', mt: { xs: 1, sm: 0 } }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {academicYearFilter !== 0 && feeTypeFilter !== 0 && (
              <Box display="flex" sx={{ justifyContent: 'end', py: 2 }}>
                <Button
                  size="small"
                  type="button"
                  color="secondary"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                  // sx={{ py: 0, px: 1 }}
                >
                  Add Row
                </Button>
              </Box>
            )}
            {academicYearFilter !== 0 && feeTypeFilter !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  border: '1px solid #e8e8e9',
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    '&::-webkit-scrollbar': {
                      height: '15px',
                    },
                  }}
                >
                  <Table
                    sx={{
                      minWidth: {
                        xs: '1200px',
                      },
                    }}
                    stickyHeader
                    aria-label="simple table"
                  >
                    <TableHead>
                      <TableRow>
                        <TableCell width={50}>
                          <Checkbox
                            aria-label="All row select"
                            indeterminate={
                              scholarshipList.length > 0 &&
                              Object.values(rowSelect).some((checked) => checked) &&
                              !isAllSelected
                            }
                            disabled={scholarshipList.length === 1 || scholarshipList.length === 0}
                            checked={isAllSelected}
                            color="primary"
                            onChange={handleSelectAll} // Add this function to handle select all
                          />
                        </TableCell>
                        <TableCell width={180}>Title</TableCell>
                        <TableCell width={150}>Type</TableCell>
                        <TableCell width={320}>Description</TableCell>
                        <TableCell width={130}>Fee Allocation</TableCell>
                        {/* <TableCell>Fee Allocation2</TableCell> */}
                        <TableCell width={200}>Status</TableCell>
                        <TableCell width={150}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {scholarshipList?.map((row: ScholarshipDetailsType) => (
                        <Formik
                          key={row.scholarshipId}
                          initialValues={{
                            title: row.title || '',
                            type: row.type || '',
                            description: row.description || '',
                            // status: row.status || 1,
                          }}
                          validationSchema={validationSchema}
                          onSubmit={(values) => handleSave(values, row)}
                        >
                          {({ values, errors, touched, handleChange, handleBlur, handleSubmit }) => {
                            const scholarshipExists = scholarshipList.some(
                              (f) => f.scholarshipId !== row.scholarshipId
                            );

                            return (
                              <TableRow
                                // sx={{ backgroundColor: rowBackgroundColors[row.scholarshipId] }}
                                hover={!rowBackgroundColors[row.scholarshipId]}
                                key={row.scholarshipId}
                              >
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  <Checkbox
                                    disabled={!row.scholarshipId}
                                    color="primary"
                                    checked={rowSelect[row.scholarshipId] || false}
                                    onChange={(event) => handleEditCheckboxChange(event, row.scholarshipId)}
                                  />
                                </TableCell>
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  {!editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId] ? (
                                    row.title
                                  ) : (
                                    <TextField
                                      // value={values.title}
                                      // onChange={handleChange}
                                      name="title"
                                      onBlur={handleBlur}
                                      error={touched.title && Boolean(errors.title)}
                                      helperText={touched.title && errors.title}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleTitleChange(e, row);
                                      }}
                                      value={row.title}
                                      placeholder="Enter title"
                                      inputProps={{
                                        style: {
                                          padding: '5px',
                                          color:
                                            rowSelect[row.scholarshipId] && !editTableRow[row.scholarshipId]
                                              ? theme.palette.common.black
                                              : '',
                                        },
                                      }}
                                      variant="outlined"
                                    />
                                  )}
                                </TableCell>

                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  {!editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId] ? (
                                    row.type === 1 ? (
                                      'Scholarship'
                                    ) : row.type === 2 ? (
                                      'Discount'
                                    ) : (
                                      ''
                                    )
                                  ) : (
                                    <>
                                      <Select
                                        // value={values.type}
                                        // onChange={handleChange}
                                        name="type"
                                        onBlur={handleBlur}
                                        error={touched.type && Boolean(errors.type)}
                                        displayEmpty
                                        value={row.type}
                                        onChange={(e) => {
                                          handleChange(e); // Pass the event to Formik
                                          const newType = parseInt(e.target.value as string, 10);
                                          setScholarshipList((prevRows) =>
                                            prevRows.map((item) =>
                                              row.id === item.id ? { ...item, type: newType } : item
                                            )
                                          );
                                        }}
                                        sx={{
                                          height: '30.5px',
                                          width: 130,
                                          color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                        }}
                                        size="small"
                                      >
                                        <MenuItem sx={{ display: 'none' }} value={-1}>
                                          Select Type
                                        </MenuItem>
                                        <MenuItem value={1}>Scholarship</MenuItem>
                                        <MenuItem value={2}>Discount</MenuItem>
                                      </Select>
                                      {touched.type && errors.type && (
                                        <FormHelperText sx={{ fontSize: 10, m: 0 }} error>
                                          {errors.type}
                                        </FormHelperText>
                                      )}
                                    </>
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  {/* <TextareaField
                          placeholder="Enter description..."
                          name={`description-${row.scholarshipId}`} // Example of dynamic name
                          value={row.description}
                          onChange={(e) => handleDescriptionChange(e, row)}
                          InputProps={{
                            inputProps: {
                              style: { resize: 'vertical', minHeight: '20px', maxHeight: '100px' },
                            },
                            // endAdornment: touched.messageContent && !!errors.messageContent && (
                            //   <InputAdornment position="end">
                            //     <ErrorIcon color="error" sx={{ mr: 2 }} />
                            //   </InputAdornment>
                            // ),
                          }}
                        /> */}
                                  {!editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId] ? (
                                    row.description
                                  ) : (
                                    <TextField
                                      multiline
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleDescriptionChange(e, row);
                                      }}
                                      value={row.description}
                                      inputProps={{
                                        style: {
                                          minWidth: 300,
                                          padding: '5px',
                                          color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                        },
                                      }}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      name="description"
                                      // value={values.description}
                                      // onChange={handleChange}
                                      onBlur={handleBlur}
                                      error={touched.description && Boolean(errors.description)}
                                      helperText={touched.description && errors.description}
                                      placeholder="Enter description"
                                      variant="outlined"
                                    />
                                  )}
                                </TableCell>
                                {/* <TableCell align="center">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => setPopup({ id: row.scholarshipId, open: true })}
                            >
                              {scholarshipValues.filter((value) => value.scholarshipId === row.scholarshipId)[0] ? (
                                <EditIcon
                                  sx={{
                                    color: rowSelect[row.scholarshipId] ? theme.palette.warning.main : '',
                                  }}
                                />
                              ) : (
                                <AddIcon />
                              )}
                            </IconButton>
                          </TableCell> */}

                                {/* ================================ */}
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  {row.scholarshipMapped !== null &&
                                  !editTableRow[row.scholarshipId] &&
                                  !rowSelect[row.scholarshipId] ? (
                                    <Button
                                      onClick={() => {
                                        setPopup2({ id: row.scholarshipId, row: row.scholarshipMapped, open: true });
                                        setShowFeeAllocationView('TableView');
                                      }}
                                      size="small"
                                      variant="contained"
                                      color="secondary"
                                      sx={{ py: 0.5, fontSize: '10px' }}
                                    >
                                      Open
                                    </Button>
                                  ) : (
                                    <Stack direction="row" justifyContent="center">
                                      <Tooltip title="Map Scholarship to fees">
                                        <IconButton
                                          size="small"
                                          // color="warning"
                                          sx={{
                                            color: rowSelect[row.scholarshipId]
                                              ? theme.palette.common.black
                                              : theme.palette.success.main,
                                          }}
                                          disabled={
                                            !rowSelect[row.scholarshipId] &&
                                            !individualSaveButtonEnabled[row.scholarshipId]
                                          }
                                          onClick={() => {
                                            setPopup2({
                                              id: row.scholarshipId,
                                              row: row.scholarshipMapped,
                                              open: true,
                                            });
                                            setShowFeeAllocationView('EditView');
                                          }}
                                          // onClick={() => handleOpenPopup(row.scholarshipMapped, row.scholarshipId)}
                                        >
                                          {editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId] ? (
                                            <AddIcon />
                                          ) : (
                                            <EditIcon />
                                          )}
                                        </IconButton>
                                      </Tooltip>
                                    </Stack>
                                  )}
                                </TableCell>
                                {/* ================================ */}
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  {!editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId] ? (
                                    <Chip
                                      size="small"
                                      label={` ${row.status === 1 ? 'Active' : row.status === 2 ? 'Inactive' : ''}`}
                                      variant="filled"
                                      color={row.status === 1 ? 'success' : 'error'}
                                    />
                                  ) : (
                                    <RadioGroup
                                      row
                                      aria-labelledby="demo-row-radio-buttons-group-label"
                                      name="row-radio-buttons-group"
                                      defaultValue={row.status}
                                      value={row.status}
                                      onChange={(e) => handleStatusChange(e, row)}
                                      sx={{ color: rowSelect[row.scholarshipId] ? theme.palette.warning.main : '' }}
                                    >
                                      <FormControlLabel
                                        control={
                                          <Radio
                                            sx={{
                                              color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                              '&.Mui-checked': {
                                                color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                              },
                                            }}
                                            size="small"
                                            value={1}
                                          />
                                        }
                                        label="Active"
                                        sx={{
                                          color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                          '& .MuiFormControlLabel-label': {
                                            color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                          },
                                        }}
                                      />
                                      <FormControlLabel
                                        control={
                                          <Radio
                                            sx={{
                                              color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                              '&.Mui-checked': {
                                                color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                              },
                                            }}
                                            size="small"
                                            value={0}
                                          />
                                        }
                                        label="Inactive"
                                        sx={{
                                          color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                          '& .MuiFormControlLabel-label': {
                                            color: rowSelect[row.scholarshipId] ? theme.palette.common.black : '',
                                          },
                                        }}
                                      />
                                    </RadioGroup>
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{
                                    backgroundColor: rowSelect[row.scholarshipId] ? theme.palette.warning.light : '',
                                  }}
                                >
                                  <Stack direction="row" gap={1}>
                                    <LoadingButton
                                      loading={individualSaveLoading[row.scholarshipId]}
                                      disabled={
                                        !rowSelect[row.scholarshipId] && !individualSaveButtonEnabled[row.scholarshipId]
                                      }
                                      variant="contained"
                                      size="small"
                                      color={!rowSelect[row.scholarshipId] ? 'success' : 'warning'}
                                      sx={{ py: 0.5, fontSize: '10px' }}
                                      onClick={handleSubmit}
                                    >
                                      {rowSelect[row.scholarshipId]
                                        ? 'Update'
                                        : !individualSaveButtonEnabled[row.scholarshipId]
                                        ? 'Saved'
                                        : 'Save'}
                                    </LoadingButton>
                                    <Button
                                      variant="contained"
                                      size="small"
                                      color="error"
                                      sx={{ py: 0.5, fontSize: '10px' }}
                                      onClick={() =>
                                        individualRemoveRowButton[row.scholarshipId]
                                          ? handleDelete(row)
                                          : handleDeleteRow(row)
                                      }
                                    >
                                      {editTableRow[row.scholarshipId] && !rowSelect[row.scholarshipId]
                                        ? 'Remove'
                                        : 'Delete'}
                                    </Button>
                                  </Stack>
                                </TableCell>
                              </TableRow>
                            );
                          }}
                        </Formik>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </ScholarshipRoot>
      {/* <PositionedSnackbar
        content="1 Row Deleted"
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        open={snackBar}
        TransitionComponent="SlideTransition"
        onClose={() => setSnackBar(false)}
        autoHideDuration={5000}
        action={action}
      /> */}
      <Snackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        open={snackBar}
        autoHideDuration={3000}
        onClose={() => setSnackBar(false)}
        // message="Receipt number already exist"
      >
        <Alert
          // onClose={() => setSnackBar(false)}
          severity="success"
          variant="filled"
          sx={{ width: '100%', color: 'white' }}
        >
          Row Deleted
        </Alert>
      </Snackbar>
      <Popup
        size="xl"
        title="Map Scholarship to Fees:"
        state={popup.open}
        onClose={handleClose}
        popupContent={
          <FeeAllocation
            isSubmitting={false}
            scholarshipId={popup.id}
            academicYearId={academicYearFilter}
            scholarshipValues={scholarshipValues}
            setScholarshipValues={setScholarshipValues}
            onClose={handleClose}
          />
        }
      />
      <Popup
        size="xl"
        title="Map Scholarship to Fees:"
        state={popup2.open}
        onClose={handleClose2}
        popupContent={
          <FeeAllocation2
            scholarshipId={popup2.id}
            showFeeAllocationView={showFeeAllocationView}
            isSubmitting={false}
            academicYearId={academicYearFilter}
            scholarshipValues={scholarshipValues}
            setScholarshipValues={setScholarshipValues}
            onClose={handleClose2}
            scholarshipValues2={popup2.row}
            // setScholarshipValues2={setScholarshipValues2}
            setScholarshipValuesArray={setScholarshipValuesArray}
            scholarshipValuesArray={scholarshipValuesArray}
            handleSave={handleSave2}
            id={id}
            feeTypeId={feeTypeFilter}
          />
        }
      />
    </Page>
  ) : (
    <ScholarshipSetting onBackClick={() => setMapToStudent('sholarship')} />
  );
}
