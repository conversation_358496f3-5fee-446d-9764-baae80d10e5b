/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';

const FeePaidListsRoot = styled.div`
  .toggle-container {
    margin: 0.5rem 1rem 0rem 1rem;
    padding: 5px;

    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    border-radius: 10px;
    @media screen and (max-width: 576px) {
      margin: 0.5rem 0.5rem 0rem 0.5rem;
    }
  }
`;

const options = [
  { path: '/manage-fee/fee-paid-lists/total', label: 'Total' },
  { path: '/manage-fee/fee-paid-lists/fee', label: 'Fee <PERSON>' },
  { path: '/manage-fee/fee-paid-lists/term', label: 'Term Wise' },
];

function FeePaidLists() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState(0);

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    const index = options.findIndex((option) => option.path === currentPath);
    if (index !== -1) {
      setFeeType(index);
    } else if (currentPath.includes('/fee-paid-lists')) {
      // Only navigate if we're actually on a fee-paid-lists route
      setFeeType(0);
      navigate(options[0].path, { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleFeeTypeChange = React.useCallback(
    (_event: React.MouseEvent<HTMLElement>, newFeeType: number | null) => {
      if (newFeeType !== null && newFeeType < options.length) {
        setFeeType(newFeeType);
        // Navigate to the correct nested route
        navigate(options[newFeeType].path, { replace: true });
      }
    },
    [navigate]
  );
  return (
      <FeePaidListsRoot>
        <div className="toggle-container">
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent={{ xs: 'center', md: 'center' }}
            flexWrap="wrap"
            gap={2}
          >
            <Typography
              variant="h6"
              fontSize={18}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Paid Lists
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="small"
              color="primary"
              sx={{
                whiteSpace: 'nowrap',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  height: '0px',
                },
                backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                border: 0,

                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontFamily: 'Poppins Semibold',
                  borderRadius: '20px',
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.common.white,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                      color: theme.palette.common.white,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.grey[200],
                    color: theme.palette.primary.main,
                  },
                },
              }}
            >
              {options.map((option, index) => (
                <ToggleButton key={option.path} value={index}>
                  {option.label}
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">
          <Outlet />
        </div>
      </FeePaidListsRoot>
  );
}

export default React.memo(FeePaidLists);
