/* eslint-disable no-nested-ternary */
import styled, { useTheme } from 'styled-components';
import Typography from '@mui/material/Typography';
import { Box, Paper, Stack, Tab, Tabs, Avatar, SelectChangeEvent, MenuItem, Select, Button, Chip } from '@mui/material';
import SwipeableViews from 'react-swipeable-views';
import AccountBalanceSharpIcon from '@mui/icons-material/AccountBalanceSharp';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import CheckIcon from '@mui/icons-material/Check';
import DescriptionIcon from '@mui/icons-material/Description';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import MobileFriendlyIcon from '@mui/icons-material/MobileFriendly';
import PrintIcon from '@mui/icons-material/Print';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewModeListData, getFeeOverviewModeListStatus } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { fetchFeeOverviewModeList } from '@/store/ManageFee/manageFee.thunks';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { GetFeeOverviewModeListType } from '@/types/ManageFee';
import { ClassListInfo } from '@/types/AcademicManagement';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { PayDrawer } from '@/components/shared/Popup/PayDrawer';
import { OverViewProps } from '@/types/Common';
import NoData from '@/assets/no-datas.png';
import { ReceiptPDF } from '@/components/shared/ReceiptPDF';
import Popup from '@/components/shared/Popup/Popup';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import UndoIcon from '@mui/icons-material/Undo';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}
const ReceiveAmountRoot = styled.div`
  display: flex;
  flex-direction: column;
  /* height: 720px; */
  /* height: calc(100vh - 20px); */
  height: calc(100vh - 155px);
  @media screen and (max-width: 576px) {
    height: calc(100vh - 100px);
  }
  .card-main-body {
    display: flex;
    flex-direction: column;
    /* max-height: calc(100% - 10px); */
    height: 100%;
    flex-grow: 1;

    .card-table-container {
      flex-grow: 1;
      width: 100%;
      /* height: 100%; */
      height: calc(100vh - 255px);
      /* height: 590px; */
      display: flex;
      flex-direction: column;
      /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
      overflow: hidden;
      .MuiTableContainer-root {
        height: 100%;
      }

      .MuiTablePagination-root {
        flex-grow: 1;
        flex-shrink: 0;
      }
    }
    .MuiTab-root {
      margin-right: 20px;
    }
    .MuiTab-root:hover {
      color: ${(props) => props.theme.palette.primary.main};
    }
  }
`;

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <div>{children}</div>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const getPaymentTypeIcon = (paymentType: string) => {
  switch (paymentType) {
    case 'Cash':
      return <CurrencyRupeeIcon fontSize="small" />;
    case 'Cheque':
      return <CheckIcon fontSize="small" />;
    case 'DD':
      return <DescriptionIcon fontSize="small" />;
    case 'Cash Deposit':
      return <AccountBalanceIcon fontSize="small" />;
    case 'Online Transfer':
      return <CreditCardIcon fontSize="small" />;
    case 'Online Upi':
    case 'Google Pay':
    case 'Phone Pay':
      return <MobileFriendlyIcon fontSize="small" />;
    case 'Netbanking':
      return <AccountBalanceSharpIcon fontSize="small" />;
    default:
      return <AccountBalanceSharpIcon fontSize="small" />;
  }
};

export const ReceiveAmount = ({ academicId, feeTypeId }: OverViewProps) => {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const [receiptPopupOpen, setReceiptPopupOpen] = React.useState<boolean>(false);
  const [receiptOpen, setReceiptOpen] = React.useState<boolean>(false);
  const [receiptId, setReceiptId] = React.useState<number>(0);
  const [receipts, setReceipt] = React.useState<'print' | 'unpay' | 'pdf' | 'approve' | ''>('');
  const [receiptDrawer, setReceiptDrawer] = React.useState<'print' | 'unpay'>('print');
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(50);

  // const toggleDrawerOpen = (id: number, receipt: 'print' | 'unpay') => {
  //   setReceiptId(id);
  //   setDrawerOpen(true);
  //   setReceiptDrawer(receipt);
  // };

  const toggleReceiptOpen = useCallback(
    (id: number, receipt: 'print' | 'unpay' | 'pdf' | 'approve' | '') => {
      setReceiptId(id);
      setReceipt(receipt);
      // if ((receipts && receipts === 'print') || (receipts && receipts === 'unpay')) {
      setReceiptOpen(true);
      // }
    },
    [setReceiptId, setReceiptOpen, setReceipt]
  );

  const popupReceiptOpen = useCallback(
    (id: number, receipt: 'print' | 'unpay' | 'pdf' | 'approve' | '') => {
      setReceiptId(id);
      setReceipt(receipt);
      setReceiptPopupOpen(true);
    },
    [setReceiptId, setReceiptPopupOpen, setReceipt]
  );

  const toggleReceiptClose = () => setReceiptOpen(false);

  const [value, setValue] = useState(0);
  const feeOverviewModeListData = useAppSelector(getFeeOverviewModeListData);
  const feeOverviewModeListStatus = useAppSelector(getFeeOverviewModeListStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  const currentReceivedAmountListRequest = React.useMemo(
    () => ({
      adminId,
      academicId,
      classId,
      feeTypeId,
    }),
    [adminId, classId, academicId, feeTypeId]
  );
  const loadReceivedAmountList = useCallback(
    async (request: { adminId: number | undefined; academicId: number; classId: number; feeTypeId: number }) => {
      try {
        const data = await dispatch(fetchFeeOverviewModeList(request)).unwrap();
        console.log('data::::', data);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewModeList({ adminId, academicId, classId, feeTypeId }));
  }, [dispatch, adminId, classId, academicId, feeTypeId]);

  const handleChangeTab = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    setCurrentPage(0);
    setRowsPerPage(50);
  };

  const handleChangeIndex = (index: number) => {
    setValue(index);
  };

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<{ value: unknown }>) => {
      const newRowsPerPage = Number(event.target.value) || 10; // Default to 10 if NaN

      if (isNaN(newRowsPerPage) || newRowsPerPage <= 0) {
        console.error('Invalid rows per page:', event.target.value);
        return;
      }

      console.log('Rows per page changed:', newRowsPerPage);

      setRowsPerPage(newRowsPerPage);
      setCurrentPage(0); // Reset to first page
    },
    [setRowsPerPage, setCurrentPage]
  );

  const totalRecords = feeOverviewModeListData?.[value]?.paidList?.length || 0;

  const handleChangePage = useCallback(
    (event: unknown, newPage: number) => {
      if (typeof newPage !== 'number') {
        console.error('Invalid page number:', newPage);
        return;
      }

      const maxPage = Math.max(0, Math.ceil(totalRecords / rowsPerPage) - 1);
      console.log(`Changing page to: ${newPage}, Max allowed: ${maxPage}`);

      if (newPage >= 0 && newPage <= maxPage) {
        setCurrentPage(newPage); // Update current page
      }
    },
    [totalRecords, rowsPerPage]
  );

  const paginatedData = useMemo(() => {
    const currentTabData = feeOverviewModeListData?.[value]?.paidList || [];
    return currentTabData.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);
  }, [feeOverviewModeListData, value, currentPage, rowsPerPage]);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 25, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords,
      onPageChange: handleChangePage,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [currentPage, rowsPerPage, totalRecords, handleChangePage, handleChangeRowsPerPage]
  );

  type PaidListType = {
    receiptId: number;
    studentId: number;
    studentName: string;
    className: string;
    createdDate: string;
    grandTotal: number;
    status: string;
  };

  const recievedFeeListColumns: DataTableColumn<PaidListType>[] = useMemo(
    () => [
      {
        name: 'receiptId',
        headerLabel: 'Sl No',
        renderCell: (row, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.receiptId}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          const { studentName, createdDate } = row;
          const formattedDate = new Date(createdDate).toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          });
          const [date, time] = formattedDate.split(' ');

          console.log('studentName::::----', studentName);
          return (
            <Stack width={200} direction="row" display="flex" alignItems="center" gap={1}>
              <Avatar src="" />
              <Stack>
                <Typography variant="subtitle2" fontSize={13}>
                  {studentName}
                </Typography>
                <Typography variant="body2" fontSize={10} color={theme.palette.secondary.main}>
                  Received on {date}&nbsp;
                  {time}
                </Typography>
              </Stack>
            </Stack>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        dataKey: 'className',
      },
      {
        name: 'Amount',
        headerLabel: 'Amount',
        renderCell: (row) => {
          const { grandTotal } = row;
          return (
            <Stack direction="row" alignItems="center" spacing={0.1} color={theme.palette.success.main}>
              <CurrencyRupeeIcon sx={{ fontSize: 14, lineHeight: 1 }} /> {/* Adjusted font size */}
              <Typography
                variant="subtitle2"
                sx={{ fontSize: 13, lineHeight: 1.2 }} // Consistent font size and alignment
              >
                {Number(grandTotal).toLocaleString('en-IN')}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'Status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              sx={{ fontWeight: 600 }}
              label={
                row.status === 'Payment Initiated'
                  ? 'Payment Initiated'
                  : row.status === 'Paid'
                  ? 'Paid'
                  : row.status === 'Failed'
                  ? 'Failed'
                  : row.status === 'Cancelled'
                  ? 'Cancelled'
                  : row.status === 'Pending'
                  ? 'Pending'
                  : ''
              }
              variant="filled"
              // color={row.status === 'Paid' ? 'success' : 'error'}
              color={
                row.status === 'Payment Initiated'
                  ? 'warning'
                  : row.status === 'Paid'
                  ? 'success'
                  : row.status === 'Failed'
                  ? 'error'
                  : row.status === 'Cancelled'
                  ? 'error'
                  : row.status === 'Pending'
                  ? 'warning'
                  : 'secondary'
              }
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        width: '150px',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={2} alignItems="center" height={30}>
              {row.status === 'Paid' && (
                <Button
                  // disabled={row.status === 'Cancelled'}
                  size="small"
                  color="error"
                  sx={{ height: 0.8, width: 90 }}
                  startIcon={<UndoIcon />}
                  variant="outlined"
                  onClick={() => toggleReceiptOpen(row.receiptId, 'unpay')}
                >
                  Unpay
                </Button>
              )}
              {row.status === 'Cancelled' && (
                <Button
                  // disabled={row.status === 'Paid'}
                  size="small"
                  color="primary"
                  sx={{ height: 0.8, width: 90 }}
                  startIcon={<SuccessIcon />}
                  variant="outlined"
                  onClick={() => toggleReceiptOpen(row.receiptId, 'approve')}
                >
                  Approve
                </Button>
              )}
              <Button
                disabled={row.status === 'Cancelled'}
                size="small"
                color="success"
                startIcon={<PrintIcon />}
                sx={{ height: 0.8, width: 70 }}
                variant="outlined"
                onClick={() => toggleReceiptOpen(row.receiptId, 'print')}
              >
                Print
              </Button>

              {/* <Link to="paid-receipt"> */}
              <Button
                size="small"
                color="info"
                disabled={row.status === 'Cancelled'}
                startIcon={<PictureAsPdfRoundedIcon />}
                sx={{ height: 0.8, width: 70 }}
                variant="outlined"
                // onClick={() => toggleReceiptOpen(row.receiptId, 'pdf')}
                onClick={() => popupReceiptOpen(row.receiptId, 'pdf')}
              >
                PDF
              </Button>
              {/* </Link> */}
            </Stack>
          );
        },
      },
    ],
    [toggleReceiptOpen, popupReceiptOpen, theme]
  );

  const getRowKey = useCallback((row: GetFeeOverviewModeListType) => row.paymentTypeId, []);

  return (
    <ReceiveAmountRoot>
      <Paper
        sx={{
          border: `1px solid #e8e8e9`,
          width: '100%',
          height: '100%',
        }}
      >
        <div className="card-main-body">
          <Box display="flex" px={2} pt={2} pb={0.5} alignItems="center" justifyContent="space-between">
            <Typography variant="h6" fontSize={14}>
              Payment Mode List
            </Typography>
            <Stack>
              <Select
                sx={{
                  backgroundColor: theme.palette.common.white,
                  color: theme.palette.primary.main,
                  height: 30,
                }}
                value={className}
                onChange={handleChange}
                displayEmpty
                labelId="demo-dialog-select-label"
                id="demo-dialog-select"
                inputProps={{ 'aria-label': 'Without label' }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: '250px', // Adjust the value to your desired height
                    },
                  },
                }}
              >
                {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
                {classDataWithAllClass?.map((item: ClassListInfo) => (
                  <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                    {item.className}
                  </MenuItem>
                ))}
              </Select>
            </Stack>
          </Box>

          <Tabs
            variant="scrollable"
            scrollButtons
            value={value}
            onChange={handleChangeTab}
            aria-label="basic tabs example"
            sx={{ borderBottom: 1, borderColor: theme.palette.grey[300], zIndex: 1 }}
          >
            {feeOverviewModeListData.map((tab, index) => (
              <Tab
                key={tab.paymentTypeId}
                label={tab.paymentType}
                icon={getPaymentTypeIcon(tab.paymentType)}
                {...a11yProps(index)}
                sx={{ fontSize: 12 }}
              />
            ))}
          </Tabs>
          {value !== -1 ? (
            <SwipeableViews
              axis={theme.direction === 'rtl' ? 'x-reverse' : 'x'}
              index={value}
              onChangeIndex={handleChangeIndex}
            >
              {feeOverviewModeListData.map((tab, index) => (
                <TabPanel key={tab.paymentTypeId} value={value} index={index}>
                  <Paper className="card-table-container">
                    <DTVirtuoso
                      showHorizontalScroll
                      tableStyles={{ minWidth: { xs: '800px' } }}
                      columns={recievedFeeListColumns}
                      data={paginatedData}
                      getRowKey={getRowKey}
                      fetchStatus={feeOverviewModeListStatus}
                      PaginationProps={pageProps}
                      allowPagination
                    />
                  </Paper>
                </TabPanel>
              ))}
            </SwipeableViews>
          ) : (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              width="100%"
              height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 100px)' }}
            >
              <Stack direction="column" alignItems="center">
                <img src={NoData} width="150px" alt="" />
                <Typography variant="subtitle2" mt={2} color="GrayText">
                  No data found !
                </Typography>
              </Stack>
            </Box>
          )}
        </div>
      </Paper>
      {receipts !== 'pdf' && (
        <TemporaryDrawer
          closeIconDisable
          onClose={toggleReceiptClose}
          state={receiptOpen}
          DrawerContent={
            <PayDrawer
              load={() => loadReceivedAmountList(currentReceivedAmountListRequest)}
              receiptDrawer={receipts}
              onClose={toggleReceiptClose}
              receiptIdNo={receiptId}
            />
          }
        />
      )}

      <Popup
        size="xl"
        state={receiptPopupOpen}
        popupContent={<ReceiptPDF receiptIdNo={receiptId} onClose={() => setReceiptPopupOpen(false)} />}
      />
    </ReceiveAmountRoot>
  );
};
