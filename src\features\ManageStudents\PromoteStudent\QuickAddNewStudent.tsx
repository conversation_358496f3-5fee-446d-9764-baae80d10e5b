// components/QuickAddNewStudent.tsx

import React from 'react';
import { TextField, Button, Stack, FormControl, Typography, Box } from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as Yup from 'yup'; // Optional if you want validation

interface QuickAddNewStudentProps {
  headers: string[]; // Dynamic field headers
  initialData?: any;
  onSave: (student: any) => void;
  onCancel?: () => void;
}

const QuickAddNewStudentRoot = styled.div`
  .MuiStack-root {
    width: 100%;
  }
`;

const QuickAddNewStudent: React.FC<QuickAddNewStudentProps> = ({ headers, initialData = {}, onSave, onCancel }) => {
  const initialValues = headers.reduce((acc, key) => {
    acc[key] = initialData?.[key] || '';
    return acc;
  }, {} as Record<string, string>);

  const validationSchema = Yup.object(
    headers.reduce((acc, key) => {
      acc[key] = Yup.string().required(`${key} is required`);
      return acc;
    }, {} as Record<string, Yup.StringSchema>)
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values) => {
      onSave(values);
    },
  });

  return (
    <QuickAddNewStudentRoot>
      <form noValidate onSubmit={formik.handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 130px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
          spacing={2}
        >
          {headers.map((header) => (
            <FormControl fullWidth key={header}>
              <Typography variant="subtitle2" fontSize={12}>
                {header}
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                name={header}
                value={formik.values[header]}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched[header] && Boolean(formik.errors[header])}
                helperText={formik.touched[header] && formik.errors[header]}
              />
            </FormControl>
          ))}
        </Stack>

        <Box sx={{ pt: 3 }}>
          <Stack spacing={2} direction="row">
            {onCancel && (
              <Button onClick={onCancel} fullWidth variant="contained" color="secondary" type="button">
                Cancel
              </Button>
            )}
            <LoadingButton fullWidth variant="contained" color="primary" type="submit" loadingPosition="start">
              Save
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </QuickAddNewStudentRoot>
  );
};

export default QuickAddNewStudent;
