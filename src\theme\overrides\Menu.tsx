import { Components, Theme } from '@mui/material';

export default function <PERSON>u(theme: Theme): Components<Theme> {
  return {
    MuiMenu: {
      styleOverrides: {
        paper: {},
      },
      defaultProps: {
        // disableScrollLock: true,
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          padding: theme.spacing(1, 2), // default padding for desktop
          '@media (max-width:600px)': {
            fontSize: 14,
            padding: theme.spacing(0.5, 1.5),
            minHeight: 32, // reduce height on mobile
          },
          '&.Mui-selected': {
            backgroundColor: theme.palette.action.selected,
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          },
        },
      },
    },
  };
}
