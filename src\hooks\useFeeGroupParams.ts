import { useParams, useNavigate } from 'react-router-dom';
import { useMemo } from 'react';

export interface FeeGroupParams {
  feeGroupId?: string;
}

export interface UseFeeGroupParamsReturn {
  feeGroupId: string | null;
  isEditMode: boolean;
  isCreateMode: boolean;
  navigateToEdit: (feeGroupId: string) => void;
  navigateToCreate: () => void;
  navigateToList: () => void;
}

/**
 * Custom hook to handle fee group route parameters and navigation
 * Provides utilities for managing fee group IDs in nested routes
 */
export function useFeeGroupParams(): UseFeeGroupParamsReturn {
  const params = useParams<FeeGroupParams>();
  const navigate = useNavigate();

  const feeGroupId = params.feeGroupId || null;
  const isEditMode = Boolean(feeGroupId);
  const isCreateMode = !isEditMode;

  const navigateToEdit = useMemo(
    () => (id: string) => {
      const currentPath = window.location.pathname;
      const basePath = currentPath.split('/').slice(0, -1).join('/');
      navigate(`${basePath}/${id}`, { replace: false });
    },
    [navigate]
  );

  const navigateToCreate = useMemo(
    () => () => {
      const currentPath = window.location.pathname;
      const basePath = currentPath.split('/').slice(0, -1).join('/');
      navigate(basePath, { replace: false });
    },
    [navigate]
  );

  const navigateToList = useMemo(
    () => () => {
      const currentPath = window.location.pathname;
      const pathParts = currentPath.split('/');
      // Remove the last part if it's a fee group ID
      if (feeGroupId) {
        pathParts.pop();
      }
      navigate(pathParts.join('/'), { replace: false });
    },
    [navigate, feeGroupId]
  );

  return {
    feeGroupId,
    isEditMode,
    isCreateMode,
    navigateToEdit,
    navigateToCreate,
    navigateToList,
  };
}

export default useFeeGroupParams;
