import * as React from 'react';
import Chip from '@mui/material/Chip';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import {
  getClassData,
  getParentsListData,
  getStaffDataList,
  getStaffListPageInfo,
  getStaffSortColumn,
  getStaffSortDirection,
  getStaffSubmitting,
  getStudentPickerData,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchParentsList } from '@/store/MessageBox/messageBox.thunks';
import SearchIcon from '@mui/icons-material/Search';
import { FiSearch } from 'react-icons/fi';
import { Box, useTheme, Avatar, Typography, IconButton } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import styled from 'styled-components';
import { fetchStudentPickerData } from '@/store/ManageFee/manageFee.thunks';
import { GetStudentPickerDataType, StudentPickerRequest } from '@/types/ManageFee';
import { fetchStaffDataList } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';

const StaffPickerRoot = styled.div`
  .MuiAutocomplete-tag {
    margin: 1px;
  }
`;
type StaffPickerFieldProps = {
  setSelectedStudentIds: React.Dispatch<React.SetStateAction<string>>;
  classId: number;
  academicId: number;
  multiple?: boolean | undefined;
  loadRecentPaidList: any;
  currentRecentPaidListRequest: any;
  componentsPropsWidth?: number | string;
  width?: number | string;
};
export default function StaffPickerField({
  loadRecentPaidList,
  currentRecentPaidListRequest,
  setSelectedStudentIds,
  classId,
  academicId,
  multiple,
  width,
  componentsPropsWidth,
}: StaffPickerFieldProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const ClassData = useAppSelector(getClassData);
  const [selectedData, setSelectedData] = React.useState<any[]>([]);
  const ParentsListData = useAppSelector(getParentsListData);
  const StudentPickerData = useAppSelector(getStudentPickerData);
  const StaffListData = useAppSelector(getStaffDataList);
  const [studentSearch, setStudentSearch] = React.useState('');
  const paginationInfo = useAppSelector(getStaffListPageInfo);
  const sortColumn = useAppSelector(getStaffSortColumn);
  const sortDirection = useAppSelector(getStaffSortDirection);
  const isSubmitting = useAppSelector(getStaffSubmitting);

  //   const currentParentListRequest: StudentPickerRequest = React.useMemo(
  //     () => ({
  //       adminId,
  //       academicId,
  //       classId,
  //       search: studentSearch,
  //     }),
  //     [adminId, academicId, classId, studentSearch]
  //   );

  const currentStaffListRequest = React.useMemo(
    () => ({
      pageNumber: 1,
      pageSize: 10,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: 11,
        staffCode: '',
        staffName: '',
        staffPhoneNumber: '',
      },
    }),
    [adminId, sortColumn, sortDirection]
  );

  React.useEffect(() => {
    if (classId !== -1 || studentSearch?.length >= 3) {
      //   dispatch(fetchStudentPickerData(currentParentListRequest));
      dispatch(fetchStaffDataList(currentStaffListRequest));
    }
  }, [dispatch, currentStaffListRequest, classId, studentSearch]);

  return (
    <StaffPickerRoot>
      <Stack spacing={3}>
        <Autocomplete
          multiple={multiple}
          id="tags-outlined"
          sx={{
            borderRadius: 10,
            width: width ?? { xs: 250, sm: 350, lg: 350 },
            '& .MuiInputBase-input': {
              padding: '0.57rem 0.675rem',
              // height: 4,
            },
          }}
          limitTags={1}
          // getLimitTagsText={1}
          options={StaffListData}
          // disableCloseOnSelect
          forcePopupIcon={false}
          openOnFocus={false}
          filterOptions={(options) => options} // Disables filtering
          disablePortal
          getOptionLabel={(option) => option.staffName || ''}
          componentsProps={{
            paper: {
              sx: {
                width: componentsPropsWidth ?? '100%',
              },
            },
          }}
          onChange={(e, newValue) => {
            // Extract studentId values and format them as a comma-separated string
            const staffIDs = newValue.map((staff) => staff.staffID).join(',');
            // setSelectedStudentIds(staffIDs);
            // Pass the formatted string to currentRecentPaidListRequest
            // loadRecentPaidList({ ...currentStaffListRequest, staffID: staffIDs });
          }}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => <Chip size="small" label={option.staffName} {...getTagProps({ index })} />)
          }
          renderOption={(props, option) => (
            <div style={{ borderBottom: `1px solid ${theme.palette.grey[200]}` }}>
              <Box component="li" sx={{}} {...props} display="flex" gap={1} alignItems="center">
                <Avatar sx={{ width: '50px', height: '50px' }} alt="" src={option.image} />
                <Box display="flex" width="100%" justifyContent="space-between">
                  <Stack direction="column" gap={0.5}>
                    <Typography width={180} variant="subtitle2" fontSize="11px" fontWeight="bold">
                      {option.staffName}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.grey[600]}>
                      Role : {option.staffJobRole}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.warning.main}>
                      Father : {option.staffFatherName}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.warning.main}>
                      Mother : {option.staffMotherName}
                    </Typography>
                  </Stack>
                  <Stack direction="column" justifyContent="space-between" alignItems="flex-end">
                    <Chip
                      variant="filled"
                      className=""
                      color="success"
                      sx={{ height: 20, fontWeight: 500 }}
                      label="Active"
                    />

                    <Typography variant="subtitle2" fontSize="11px" color={theme.palette.primary.main}>
                      Code : {option.staffCode}
                    </Typography>
                    <Typography visibility="hidden" variant="subtitle2" fontSize="11px" color={theme.palette.info.main}>
                      {option.staffID}
                    </Typography>
                    <Typography variant="subtitle2" fontSize="11px" color={theme.palette.info.main}>
                      {option.staffPhoneNumber}
                    </Typography>
                  </Stack>
                </Box>
              </Box>
            </div>
          )}
          filterSelectedOptions
          renderInput={(params) => (
            <TextField
              variant="outlined"
              {...params}
              InputProps={{
                ...params.InputProps,
                style: {
                  //  borderRadius: 50,
                  paddingLeft: 30,
                },
                endAdornment: (
                  <Stack sx={{ position: 'absolute', left: 10 }}>
                    <FiSearch color={theme.palette.grey[600]} />
                    {/* <SearchIcon fontSize="small" color="secondary" /> */}
                  </Stack>
                ),
                disableUnderline: true,
              }}
              placeholder="Search"
              onChange={(e) => {
                const { value } = e.target;
                if (value.length >= 3) {
                  setStudentSearch(value);
                } else {
                  setStudentSearch(''); // Clear the search state if input is less than 3 characters
                }
              }}
            />
          )}
        />
      </Stack>
    </StaffPickerRoot>
  );
}
