/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-unstable-nested-components */
import * as React from 'react';
import { DemoContainer, DemoItem } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { PickersActionBar } from '@mui/x-date-pickers';
import { useTheme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import { InputAdornment } from '@mui/material';

type DateSelectProps = {
  value?: dayjs.Dayjs | null | undefined;
  name?: string;
  onChange?: (newValue: dayjs.Dayjs | null) => void;
  maxDate?: dayjs.Dayjs;
  minDate?: dayjs.Dayjs;
  variant?: {};
  fullWidth?: string | undefined;
  padding?: {};
  defaultValue?: any;
  disabled?: boolean;
  width?: string | {};
  bgcolor?: string;
  inputFeildPadding?: string;
  inputFeildBgColor?: string;
  inputFeildColor?: string;
  DateIconSize?: string;
  DateIconButtonSize?: string;
  maxWidth?: string | {};
  fontSize?: string | number;
  inputRef?: React.Ref<HTMLInputElement>;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  error?: boolean;
  helperText?: string;
  startAdornment?: React.ReactNode;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
};

export default function DatePickers({
  value,
  name,
  onChange,
  maxDate,
  variant,
  fullWidth,
  padding,
  defaultValue,
  disabled,
  width,
  bgcolor,
  inputFeildPadding,
  inputFeildBgColor,
  inputFeildColor,
  DateIconSize,
  DateIconButtonSize,
  minDate,
  maxWidth,
  fontSize,
  inputRef,
  onKeyDown,
  error,
  helperText,
  startAdornment,
  onBlur,
}: DateSelectProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DemoContainer
        sx={{
          pt: 0,
          '&::-webkit-scrollbar': {
            width: '0px',
          },
          width,
        }}
        components={['DatePicker']}
      >
        <DemoItem>
          <DatePicker
            disabled={disabled}
            sx={{
              minWidth: fullWidth ?? '100%',
              maxWidth,
              width,
              border: 0,
              pt: padding,
              bgcolor,
              borderRadius: 1,
            }}
            format="DD/MM/YYYY"
            name={name}
            value={value}
            defaultValue={defaultValue}
            onChange={onChange}
            minDate={minDate}
            maxDate={maxDate}
            inputRef={inputRef}
            // onKeyDown={onKeyDown}
            slotProps={{
              textField: {
                onBlur,
                onKeyDown,
                name,
                error: error ?? false,
                helperText,
                // variant: 'standard',
                // fullWidth: true,
                InputProps: {
                  startAdornment: error ? (
                    <InputAdornment position="end" sx={{ position: 'absolute', right: 30 }}>
                      {startAdornment}
                    </InputAdornment>
                  ) : (
                    ''
                  ),
                  sx: {
                    backgroundColor:
                      variant === 'standard'
                        ? inputFeildBgColor ?? isLight
                          ? theme.palette.grey[200]
                          : theme.palette.grey[900]
                        : undefined,
                    color: inputFeildColor,
                    ...(variant === 'standard' && {
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      // '&.MuiInput-root': {
                      //   borderBottom: 'none',
                      //   borderRadius: 4,
                      // },
                      // '&.MuiInput-root:before': {
                      //   borderBottom: 'none',
                      // },
                      // '&.MuiInput-root:after': {
                      //   borderBottom: 'none',
                      // },
                      // '&.MuiInput-root:hover:not(.Mui-disabled):before': {
                      //   borderBottom: 'none',
                      // },
                    }),
                    '& input::placeholder': {
                      color: theme.palette.grey[600],
                      opacity: 1,
                    },
                  },
                },
                inputProps: {
                  sx: {
                    padding: inputFeildPadding ?? '0.57rem 0.675rem',
                    fontSize,
                  },
                },
              },
              openPickerButton: {
                sx: {
                  padding: DateIconButtonSize ?? 1,
                  right: 2,
                  '& svg': {
                    fontSize: DateIconSize ?? 19,
                  },
                },
                // onBlur,
                onKeyDown,
              },
              ...variant,
              actionBar: {
                actions: ['clear', 'accept'], // Show "clear" and "accept" buttons
              },
            }}
            slots={{
              actionBar: (props: any) => (
                <PickersActionBar
                  {...props}
                  actions={['clear', 'accept']}
                  onClear={props.onClear} // Default "clear" behavior
                  onAccept={() => {
                    console.log('OK clicked');
                    props.onAccept?.(); // Default "ok" behavior
                  }}
                />
              ),
            }}
          />
        </DemoItem>
      </DemoContainer>
    </LocalizationProvider>
  );
}
