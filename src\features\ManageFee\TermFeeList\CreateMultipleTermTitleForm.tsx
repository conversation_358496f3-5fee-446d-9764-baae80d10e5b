import { useEffect, useCallback, useRef, useState, FormEvent } from 'react';
import { FEE_TYPE_ID_OPTIONS, FEE_TYPE_OPTIONS, GENDER_SELECT, STATUS_OPTIONS } from '@/config/Selection';
import { StudentCreateRow, StudentListInfo } from '@/types/StudentManagement';
import {
  Stack,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  Paper,
  TableBody,
  Grid,
  TableContainer,
  TextField,
  MenuItem,
  Select,
  IconButton,
  Card,
  Tooltip,
  Alert,
  Avatar,
  useTheme,
} from '@mui/material';
import { FormikErrors, useFormik } from 'formik';
import { MdArrowBack } from 'react-icons/md';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import Page from '@/components/shared/Page';
import ErrorIcon from '@mui/icons-material/Error';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { addNewClassMulti } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import * as Yup from 'yup';
import api from '@/api';
import BackButton from '@/components/shared/BackButton';
import useSettings from '@/hooks/useSettings';
import { keyframes } from '@emotion/react';
import dayjs from 'dayjs';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getStudentSubmitting, getYearData } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { ClassListInfo } from '@/types/AcademicManagement';
import { addNewMultipleStudent } from '@/store/Students/studentManagement.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import ProfileImage from '@/components/shared/ProfileImage';
import { types } from 'node:util';

export type AddStudentMultipleProps = {
  onBackClick: () => void;
  studentDetail?: StudentListInfo;
};

export type AddStudentMultipleState = {
  students: StudentCreateRow[];
};

const CreateMultipleTermTitleFormRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;

    form {
      height: calc(100% - 40px);
      display: flex;
      flex-direction: column;

      .form-container {
        flex-grow: 1;
        height: calc(100% - 84px);

        .MuiTableContainer-root {
          height: 100%;
          overflow: auto;
          overflow-x: hidden;
        }
      }

      .button-container {
        border-top: 1px solid #ddd;
      }
    }
    .icon-wrapper {
      position: absolute;
      bottom: 0px;
      right: 0px;
    }
    .date-feild {
      .MuiStack-root {
        width: 100%;
      }
    }
    @media screen and (max-width: 380px) {
      form {
        height: calc(100% - 70px);
      }
      .student_info_header {
        flex-wrap: wrap;
      }
    }
  }
`;

const rotateAnimation = keyframes`
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
`;
function CreateMultipleTermTitleForm({ onBackClick, studentDetail }: AddStudentMultipleProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<any>(null);
  const textBoxRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSolid, setIsSolid] = useState(false);

  const ClassData = useAppSelector(getClassData);
  const isSubmitting = useAppSelector(getStudentSubmitting);

  const formValidationSchema = Yup.object({
    fee: Yup.array().of(
      Yup.object({
        feeTitle: Yup.string().required('Please enter Fee Title'),
        startDate: Yup.string().required('Please enter Start Date'),
        endDate: Yup.string().required('Please enter End Date'),
        feeTypeId: Yup.number().oneOf([1, 2], 'Please select Type'),
      })
    ),
  });

  const AllClassOption = {
    classId: 0,
    className: 'Select Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];

  const initialRowKey = uuidv4();

  const defaultRow: StudentCreateRow = {
    rowKey: initialRowKey,
    feeTitle: '',
    feeTypeId: 0,
    startDate: '',
    endDate: '',
    createdBy: adminId,
  };

  const { values, handleChange, handleBlur, setFieldTouched, handleSubmit, setFieldValue, touched, errors, resetForm } =
    useFormik<AddStudentMultipleState>({
      initialValues: {
        fee: [defaultRow],
      },
      onSubmit: async (data) => {
        try {
          const response = await dispatch(addNewMultipleStudent(data.fee)).unwrap();
          // const response = { inserted: 1 };

          if (response.inserted) {
            const successMessage = (
              <SuccessMessage
                loop={false}
                jsonIcon={Success}
                message={`${data.students.length} Fee created successfully`}
              />
            );
            await confirm(successMessage, 'Fee Created', { okLabel: 'Ok', showOnlyOk: true });
            resetForm();
            setFieldValue('fee', []);
            if (response.inserted) {
              setError(null);
              onBackClick();
            } else {
              setError('Something went wrong in creating fee');
            }
          } else {
            const errorMessage = <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Fee created failed" />;
            await confirm(errorMessage, 'Fee Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        } catch {
          setError('Something went wrong in creating fee');
          const errorMessage = <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Fee created failed" />;
          await confirm(errorMessage, 'Fee Create', { okLabel: 'Ok', showOnlyOk: true });
        }
      },
      validateOnBlur: false,
      validationSchema: formValidationSchema,
      validate: (feeVals) => {
        const errorObj: any = {};
        feeVals.fee.forEach(async (feeRow, rowIndex, arr) => {
          if (arr.some((x, i) => feeRow.feeTitle !== '' && x.feeTitle === feeRow.feeTitle && i !== rowIndex)) {
            if (!errorObj.fee) {
              errorObj.fee = [];
            }
            errorObj.fee[rowIndex] = {};
            errorObj.fee[rowIndex].feeTitle = 'Duplicate fee title';
          }
        });
        return errorObj;
      },
    });

  useEffect(() => {
    textBoxRefs.current[values.fee.length - 1]?.focus();
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [values.fee.length]);

  const handleAddNewRow = () => {
    const newRow = {
      feeTitle: '',
      feeTypeId: 0,
      startDate: '',
      endDate: '',
      createdBy: adminId,
      rowKey: uuidv4(), // Ensure each row has a unique key
    };
    setFieldValue('fee', [...values.fee, newRow]);
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>, rowIndex: number) => {
    if (event.target.files && event.target.files.length > 0) {
      setIsAnimating(true); // Start the animation
      const file = event.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      setSelectedImage(imageUrl);
      setFieldValue(`fee[${rowIndex}].studentImage`, imageUrl);
      // Clear input value to allow re-uploading the same file
      event.target.value = '';
    }
  };

  const fileInputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const setFileInputRef = useCallback((el: HTMLInputElement | null, rowIndex: number) => {
    fileInputRefs.current[rowIndex] = el;
  }, []);

  const handleAvatarClick = (rowIndex: number) => {
    fileInputRefs.current[rowIndex]?.click();
  };
  // const handleAvatarClick = () => {
  //   document.getElementById('imageInput')?.click();
  // };

  const handleRemoveImage = (rowIndex: number) => {
    setSelectedImage('');
    setFieldValue(`staff[${rowIndex}].staffImage`, '');
    setIsAnimating(false); // Stop the animation when image is removed
  };

  const handleRowDelete = (rowkey: string) => {
    const updatedRows = values.fee.filter((x) => x.rowKey !== rowkey);
    setFieldValue('fee', updatedRows);
  };

  const hasFeeTitleFieldError = (rowIndex: number) => {
    if (touched.fee && touched.fee.length > 0 && errors.fee && errors.fee.length > 0) {
      return (
        !!touched.fee[rowIndex]?.feeTitle && !!(errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.feeTitle
      );
    }
    return false;
  };
  const getFeeTitleFieldError = (rowIndex: number) => {
    return (errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.feeTitle;
  };

  const hasTypeFieldError = (rowIndex: number) => {
    if (touched.fee && touched.fee.length > 0 && errors.fee && errors.fee.length > 0) {
      return (
        !!touched.fee[rowIndex]?.feeTypeId && !!(errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.feeTypeId
      );
    }
    return false;
  };
  const getTypeFieldError = (rowIndex: number) => {
    return (errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.feeTypeId;
  };

  const hasStartDateFieldError = (rowIndex: number) => {
    if (touched.fee && touched.fee.length > 0 && errors.fee && errors.fee.length > 0) {
      return (
        !!touched.fee[rowIndex]?.startDate && !!(errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.startDate
      );
    }
    return false;
  };
  const getStartDateFieldError = (rowIndex: number) => {
    return (errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.startDate;
  };

  const hasEndDateFieldError = (rowIndex: number) => {
    if (touched.fee && touched.fee.length > 0 && errors.fee && errors.fee.length > 0) {
      return !!touched.fee[rowIndex]?.endDate && !!(errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.endDate;
    }
    return false;
  };
  const getEndDateFieldError = (rowIndex: number) => {
    return (errors.fee as FormikErrors<StudentCreateRow>[])[rowIndex]?.endDate;
  };

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
      setFieldValue('fee', [defaultRow]);
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  return (
    <Page title="Create Multiple Classes">
      <CreateMultipleTermTitleFormRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            mb={2}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            className="student_info_header"
            spacing={2}
          >
            <BackButton onBackClick={onBackClick} title="Create Term Title" />
            <Stack direction="row" justifyContent="end" width="100%">
              <Button
                type="button"
                variant="contained"
                size="small"
                sx={{ borderRadius: 10 }}
                startIcon={<AddIcon />}
                onClick={handleAddNewRow}
              >
                Add New Row
              </Button>
            </Stack>
          </Stack>
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <div className="form-container">
              {!!error && (
                <Alert color="error" sx={{ marginBottom: '10px' }}>
                  {error}
                </Alert>
              )}

              <TableContainer component={Paper} ref={scrollContainerRef}>
                {values.fee.map((feeRow, rowIndex) => (
                  <Card
                    key={feeRow.rowKey}
                    sx={{
                      border: 1,
                      mb: 2,
                      p: 3,
                      boxShadow: 0,
                      bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                      borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[900],
                    }}
                  >
                    <Grid
                      container
                      columnSpacing={2}
                      rowSpacing={1}
                      sx={rowIndex === values.fee.length - 1 ? { '& td': { border: 0 } } : null}
                    >
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={3} xxl={2.4}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Fee Title
                        </Typography>
                        <TextField
                          name={`fee[${rowIndex}].feeTitle`}
                          value={feeRow.feeTitle}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter title"
                          fullWidth
                          error={hasFeeTitleFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasFeeTitleFieldError(rowIndex) && (
                              <Tooltip title={getFeeTitleFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={3} xxl={2.4}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Type
                        </Typography>
                        <Select
                          disabled={isSubmitting}
                          fullWidth
                          name={`fee[${rowIndex}].feeTypeId`}
                          value={feeRow.feeTypeId}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Select type"
                          error={hasTypeFieldError(rowIndex)}
                          endAdornment={
                            hasTypeFieldError(rowIndex) && (
                              <Tooltip title={getTypeFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ position: 'relative', right: 10 }} />
                              </Tooltip>
                            )
                          }
                        >
                          <MenuItem value={0}>Select Type</MenuItem>
                          {FEE_TYPE_ID_OPTIONS.map((opt) => (
                            <MenuItem key={opt.id} value={opt.id}>
                              {opt.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={2.4} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Start Date
                        </Typography>
                        <DatePickers
                          // width="340px "
                          name={`fee[${rowIndex}].startDate`}
                          value={dayjs(feeRow.startDate, 'DD/MM/YYYY')}
                          onBlur={handleBlur}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`fee[${rowIndex}].startDate`, formattedDate);
                          }}
                          error={hasStartDateFieldError(rowIndex)}
                          // helperText={touched.startDate && errors.startDate ? errors.startDate : ''}
                          startAdornment={
                            hasStartDateFieldError(rowIndex) && (
                              <Tooltip title={getStartDateFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={2.4} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          End Date
                        </Typography>
                        {/* <DatePickers
                          // width="340px "
                          name={`fee[${rowIndex}].admissionDate`}
                          value={dayjs(feeRow.admissionDate, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`fee[${rowIndex}].admissionDate`, formattedDate);
                          }}
                        /> */}
                        <DatePickers
                          name={`fee[${rowIndex}].endDate`}
                          value={dayjs(feeRow.admissionDate, 'DD/MM/YYYY')}
                          onBlur={handleBlur}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`fee[${rowIndex}].endDate`, formattedDate);
                          }}
                          // inputRef={(el) => {
                          //   if (rowIndex === values.fee.length - 1) {
                          //     textBoxRefs.current[rowIndex] = el;
                          //   }
                          // }}
                          onKeyDown={(e) => {
                            if ((e.key === 'Enter' || e.key === 'Tab') && rowIndex === values.fee.length - 1) {
                              const currentRow = values.fee[rowIndex];
                              const requiredFields = [
                                'feeTitle',
                                'feeType',
                                'feeTypeId',
                                'startDate',
                                'endDate',
                                'createdBy',
                                'rowKey',
                              ];
                              const isValid = requiredFields.every((field) => currentRow[field]);

                              if (isValid) {
                                e.preventDefault(); // Prevent default tab behavior
                                handleAddNewRow();
                              } else {
                                // Mark all required fields as touched to trigger error display
                                requiredFields.forEach((field) => {
                                  setFieldTouched(`fee[${rowIndex}].${field}`, true, true);
                                });
                              }
                            }
                          }}
                          error={hasEndDateFieldError(rowIndex)}
                          // helperText={touched.startDate && errors.startDate ? errors.startDate : ''}
                          startAdornment={
                            hasEndDateFieldError(rowIndex) && (
                              <Tooltip title={getEndDateFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        />
                      </Grid>
                    </Grid>
                    <Stack position="absolute" right={3} top={3}>
                      <IconButton
                        aria-label="Delete Row"
                        color="error"
                        size="small"
                        onClick={() => handleRowDelete(feeRow.rowKey)}
                        disabled={values.fee.length === 1}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Stack>
                  </Card>
                ))}
              </TableContainer>
            </div>
            <Stack className="button-container" direction="row" justifyContent="end" gap={1} pt={1}>
              <Button type="button" color="secondary" variant="contained" onClick={handleReset}>
                Reset
              </Button>
              <Button color="primary" variant="contained" type="submit">
                Save
              </Button>
            </Stack>
          </form>
        </Card>
      </CreateMultipleTermTitleFormRoot>
    </Page>
  );
}

export default CreateMultipleTermTitleForm;
