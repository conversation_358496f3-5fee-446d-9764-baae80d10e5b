/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';

const FeeListGroupRoot = styled.div`
  .toggle-container {
    margin: 0.5rem 1rem 0rem 1rem;
    padding: 5px;

    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    border-radius: 10px;
    @media screen and (max-width: 576px) {
      margin: 0.5rem 0.5rem 0rem 0.5rem;
    }
  }

  .content-container {
  }
`;

type FeeType = 'basic' | 'term';

function FeeListGroup() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState<FeeType>('basic');

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath.includes('/fee-lists/term')) {
      setFeeType('term');
    } else if (currentPath.includes('/fee-lists/basic')) {
      setFeeType('basic');
    } else if (currentPath === '/manage-fee/fee-lists') {
      // Only navigate if we're exactly on the parent route
      setFeeType('basic');
      navigate('/manage-fee/fee-lists/basic', { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleFeeTypeChange = (_event: React.MouseEvent<HTMLElement>, newFeeType: FeeType | null) => {
    if (newFeeType !== null) {
      setFeeType(newFeeType);
      // Update URL based on selection
      if (newFeeType === 'basic') {
        navigate('/manage-fee/fee-lists/basic', { replace: true });
      } else {
        navigate('/manage-fee/fee-lists/term', { replace: true });
      }
    }
  };
  return (
    <FeeListGroupRoot>
      <div className="toggle-container">
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent={{ xs: 'center', md: 'center' }}
          flexWrap="wrap"
          gap={2}
        >
          <Typography
            variant="h6"
            fontSize={18}
            fontWeight={600}
            // textTransform='uppercase'
            color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
          >
            Fee Management
          </Typography>
          <ToggleButtonGroup
            value={feeType}
            exclusive
            onChange={handleFeeTypeChange}
            aria-label="Fee Type Selection"
            size="small"
            color="primary"
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                height: '0px',
              },
              backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
              border: 0,

              '& .MuiToggleButton-root': {
                px: 3,
                py: 1,
                fontFamily: 'Poppins Semibold',
                borderRadius: '20px',
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main,
                  color: theme.palette.common.white,
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                    color: theme.palette.common.white,
                  },
                },
                '&:hover': {
                  backgroundColor: theme.palette.grey[200],
                  color: theme.palette.primary.main,
                },
              },
            }}
          >
            <ToggleButton value="basic">Fee Wise</ToggleButton>
            <ToggleButton value="term">Term Wise</ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </div>

      <div className="content-container">
        <Outlet />
      </div>
    </FeeListGroupRoot>
  );
}

export default React.memo(FeeListGroup);
