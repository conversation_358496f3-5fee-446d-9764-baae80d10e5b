/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { Dispatch, FormEvent, ReactElement, SetStateAction, useCallback, useEffect, useState } from 'react';
import {
  Divider,
  Checkbox,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  TextField,
  Autocomplete,
  IconButton,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import errorIcon from '@/assets/ManageFee/Error.json';
import DeleteIcon from '@mui/icons-material/Delete';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getFineMappingListStatus, getManageFeeSubmitting, getYearData } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { DeleteFineMap, createFineMap, fetchFineMappingList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { FineMapDeleteDataType, FineMapInsertDataType } from '@/types/ManageFee';
import paidSuccess from '@/assets/ManageFee/paidSuccess.json';
import Lottie from 'lottie-react';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';

const FeeAllocationRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
  .MuiTableCell-root {
    border: 1px solid ${(props) => props.theme.palette.grey[200]};
    border-radius: 0px;
  }
  .MuiTableCell-head {
    color: ${(props) => props.theme.palette.grey[700]};
    background: ${(props) => (props.theme.themeMode === 'light' ? '#FFBB63' : props.theme.palette.grey[900])};
  }
  .MuiTableCell-head:nth-child(1) {
    z-index: 11;
    position: sticky;
    left: 0;
  }
  .MuiTableCell-head:nth-child(2) {
    z-index: 11;
    position: sticky;
    left: 50px;
  }
  .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
    position: sticky;
    left: 0;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#FFDB99' : props.theme.palette.grey[900])};
    z-index: 1;
  }
  .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
    position: sticky;
    left: 50px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#FFDB99' : props.theme.palette.grey[900])};
    z-index: 1;
  }
  .MuiTableCell-root:first-child {
    padding: 7px;
  }
  .MuiTableCell-root.MuiTableCell-body:nth-child(n + 3) {
    padding: 0;
  }
`;
export type ScholarshipAllocationType = {
  feeId: number;
  termId: number;
  type: number;
  value: string;
  dbResult: string;
  scholarshipId: number;
  scholarshipMapId: number;
};

export type FineMappedListType = {
  feeId: number;
  fineMappedTerms: {
    fineMappedId: number;
    termId: number;
    fineId: number;
  };
};
export type FeeAllocationProps = {
  scholarshipId?: number;
  academicYearId: number;
  scholarshipValues?: ScholarshipAllocationType[];
  onClose: () => void;
  setScholarshipValues?: Dispatch<SetStateAction<ScholarshipAllocationType[]>>;
  scholarshipValues2?: any;
  setScholarshipValues2?: any;
  setScholarshipValuesArray?: any;
  scholarshipValuesArray?: any;
  handleSave?: () => void;
  id?: number;
  feeTypeId: number;
};
interface FineMappedValue {
  feeId: number;
  termId: number;
  fineId: number;
}
interface FineMappedValuesByFeeId {
  [key: number]: {
    fineMappedId: number;
    termId: number;
    fineId: number;
    dbResult: string;
    dbResultId: number;
  }[];
}
interface SavedStatus {
  [key: number]: number[];
}

type FineMappedTermsArray = {
  fineMappedId: number;
  termId: number;
  fineId: number;
}[];

function FeeAllocation({ onClose, academicYearId, feeTypeId }: FeeAllocationProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const YearData = useAppSelector(getYearData);
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(feeTypeId);
  const ScholarshipFeeStatus = useAppSelector(getFineMappingListStatus);
  const isSubmitting = useAppSelector(getManageFeeSubmitting);
  const [basicFees, setBasicFees] = useState([{ feeId: 0, feeTitle: '' }]);
  const [termFees, setTermFees] = useState([{ termId: 0, termTitle: '' }]);
  const [fineTypes, setFineTypes] = useState([{ fineId: 0, title: '' }]);
  const [fineMappedList, setFineMappedList] = useState<FineMappedListType[]>([]);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [checkedItems, setCheckedItems] = useState<{ [key: string]: boolean }>({});
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [disabledFields, setDisabledFields] = useState<{ [key: string]: boolean }>({});
  const [fineMappedValues, setFineMappedValues] = useState<FineMappedValue[]>([]);
  const [savedStatus, setSavedStatus] = useState({});

  const initialFeeAllocationListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentFeeAllocationListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );

  const loadFeeAllocationList = useCallback(
    async (request: { adminId: number; academicId: number; feeTypeId: number }) => {
      try {
        const data = await dispatch(fetchFineMappingList(request)).unwrap();
        setBasicFees(data.feeList);
        setTermFees(data.termList);
        setFineTypes(data.fineTypeList);
        setFineMappedList(data.fineMappedList);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Scholarship list:', error);
      }
    },
    [dispatch]
  );

  // useEffect(() => {
  //   // Save scholarshipValuesArray to local storage whenever it changes
  //   localStorage.setItem('scholarshipValuesArray', JSON.stringify(scholarshipValuesArray));
  // }, [scholarshipValuesArray]);

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadFeeAllocationList(initialFeeAllocationListRequest);
  }, [dispatch, adminId, initialFeeAllocationListRequest, loadFeeAllocationList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeeAllocationList({ ...currentFeeAllocationListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const typeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(typeId);
    loadFeeAllocationList({ ...currentFeeAllocationListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };
  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setAcademicYearFilter(0);
    setDisabledCheckBoxes([]);
    setFeeTypeFilter(0);
    // loadFeeAllocationList(currentFeeAllocationListRequest);
  }, []);

  const handleCancel = () => {
    loadFeeAllocationList(currentFeeAllocationListRequest);
    setDisabledCheckBoxes([]);
  };
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadFeeAllocationList(currentFeeAllocationListRequest);
      setSelectedRows([]);
    },
    [loadFeeAllocationList, currentFeeAllocationListRequest]
  );

  const getRowKey = useCallback((row: { feeId: number; feeTitle: string }) => row.feeId, []);

  const handleRowCheckboxChange = (feeId: number) => {
    const isRowSelected = selectedRows.includes(feeId);
    const updatedSelectedRows = isRowSelected ? selectedRows.filter((id) => id !== feeId) : [...selectedRows, feeId];
    setSelectedRows(updatedSelectedRows);

    const fineMappedListArray = fineMappedList.filter((m) => m.feeId === feeId); // Filter the list to match the row's feeId
    console.log('fineMappedListArray', fineMappedListArray);

    const updatedCheckedItems = { ...checkedItems };
    const updatedDisabledFields = { ...disabledFields };
    termFees.forEach((term) => {
      const key = `${feeId}-${term.termId}`;
      updatedCheckedItems[key] = !isRowSelected;
      updatedDisabledFields[key] = !updatedCheckedItems[key];
    });
    setCheckedItems(updatedCheckedItems);
    setDisabledFields(updatedDisabledFields);
  };

  const handleAllCheckboxChange = () => {
    const newIsAllSelected = !isAllSelected;
    setIsAllSelected(newIsAllSelected);

    const updatedSelectedRows = newIsAllSelected ? basicFees.map((row) => row.feeId) : [];
    setSelectedRows(updatedSelectedRows);
  };

  // const handleTypeChange = (feeId: number, termId: number, newValue: { fineId: number; title: string } | null) => {
  //   const { fineId } = newValue;
  //   if (fineId == null) {
  //     // If fineId is null or undefined, remove the mapping for this cell
  //     setFineMappedValues((prev) => prev.filter((item) => !(item.feeId === feeId && item.termId === termId)));
  //     return;
  //   }
  //   // Get the current value for the feeId and termId combination
  //   const existingIndex = fineMappedValues.findIndex((item) => item.feeId === feeId && item.termId === termId);

  //   // If the combination exists, update the type property
  //   if (existingIndex !== -1) {
  //     const updatedFineMappedValues = fineMappedValues.map((item, index) => {
  //       if (index === existingIndex) {
  //         return { ...item, fineId };
  //       }
  //       return item;
  //     });
  //     setFineMappedValues(updatedFineMappedValues);
  //   } else {
  //     const newFineMappedValues = {
  //       feeId,
  //       termId,
  //       fineId, // Assuming type is always 1 for text field changes
  //     };
  //     setFineMappedValues([...fineMappedValues, newFineMappedValues]);
  //   }
  //   // You can log the updated values for debugging if needed
  //   console.log(fineMappedValues);
  //   console.log('fineId::::----', fineId);
  //   console.log(feeId);
  //   console.log(termId);
  // };

  const handleTypeChange = (feeId: number, termId: number, newValue: { fineId: number; title: string } | null) => {
    if (!newValue) {
      // Clear mapping for this feeId and termId
      setFineMappedValues((prev) => prev.filter((item) => !(item.feeId === feeId && item.termId === termId)));
      return;
    }

    const { fineId } = newValue;

    const existingIndex = fineMappedValues.findIndex((item) => item.feeId === feeId && item.termId === termId);

    if (existingIndex !== -1) {
      const updatedFineMappedValues = fineMappedValues.map((item, index) =>
        index === existingIndex ? { ...item, fineId } : item
      );
      setFineMappedValues(updatedFineMappedValues);
    } else {
      setFineMappedValues([...fineMappedValues, { feeId, termId, fineId }]);
    }
  };

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  const handleSave = useCallback(async () => {
    try {
      // Extract feeId from fineMappedValues and get unique feeIds
      const feeIdArray = fineMappedValues.map((f) => f.feeId);
      const uniqueFeeIds = [...new Set(feeIdArray)];

      // Group fineMappedValues by feeId
      const fineMappedValuesByFeeId = fineMappedValues.reduce<FineMappedValuesByFeeId>((acc, m) => {
        if (!acc[m.feeId]) {
          acc[m.feeId] = [];
        }
        acc[m.feeId].push({
          fineMappedId: 0,
          termId: m.termId,
          fineId: m.fineId,
          dbResult: '',
          dbResultId: 0,
        });
        return acc;
      }, {});

      // Process each unique feeId
      await Promise.all(
        uniqueFeeIds.map(async (feeId) => {
          // Construct the sendreq object with the current feeId and its corresponding fineMappedValues
          const sendreq = [
            {
              adminId,
              accademicId: academicYearFilter,
              feeId, // Ensure the feeId is correctly set here
              dbResult: '',
              feeTypeId: feeTypeFilter,
              fineMappedValues: fineMappedValuesByFeeId[feeId], // Set the specific fineMappedValues for this feeId
            },
          ];

          console.log('sendreq::::----', sendreq);

          // Dispatch the createFineMap action
          const response = await dispatch(createFineMap(sendreq)).unwrap();

          if (response) {
            console.log('response::::----', response);
            loadFeeAllocationList(currentFeeAllocationListRequest);
            await showConfirmation(
              <SuccessMessage loop={false} jsonIcon={paidSuccess} message="Fine Map successfully" />,
              ''
            );
            setSavedStatus((prev) => ({
              ...prev,
              [feeId]: fineMappedValuesByFeeId[feeId].map((val) => val.termId),
            }));
            // Clear the saved status after 5 seconds
            setTimeout(() => {
              setSavedStatus((prev) => {
                const newState: SavedStatus = { ...prev };
                delete newState[feeId];
                return newState;
              });
            }, 10000);
            // setFineMappedValues((prev) =>
            //   prev.map((item) => {
            //     if (item.feeId === feeId) {
            //       // Assuming response has the updated fineMappedValues
            //       const updatedFineMappedValues = fineMappedValuesByFeeId[feeId];
            //       return { ...item, fineMappedValues: updatedFineMappedValues };
            //     }
            //     return item;
            //   })
            // );
          } else {
            await showConfirmation(
              <ErrorMessage loop={false} jsonIcon={errorIcon} message="Failed. Please try again." />,
              ''
            );
          }
        })
      );
    } catch (err) {
      await showConfirmation(
        <ErrorMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again." />,
        ''
      );
      console.error(err);
    }
  }, [fineMappedValues, academicYearFilter, adminId, dispatch]);

  const handleDeleteCell = useCallback(
    async (row: FineMappedTermsArray, term: { termId: number; termTitle: string }, fineTitle: string | undefined) => {
      const { fineId } = row;
      const fineMappedIds = row.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{fineTitle}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = fineMappedIds.map((fineMappedId) => ({
          adminId,
          accademicId: academicYearFilter,
          fineMappedId,
          dbResult: '',
          deletedId: 0,
        }));

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteFineMap(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            loadFeeAllocationList(currentFeeAllocationListRequest);
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme]
  );

  const handleDeleteMultiple = useCallback(async () => {
    console.log('selectedRows', selectedRows);
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Scholarhip?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      // const sendRequests = await Promise.all(
      //   Object.keys(selectedRows)
      //     .map(async (key: string) => {
      //       const numericKey = Number(key);
      //       if (selectedRows[numericKey]) {
      //         return {
      //           adminId,
      //           accademicId: academicYearFilter,
      //           fineMappedId: numericKey, // use the key as termId
      //           dbResult: '',
      //           deletedId: 0,
      //         };
      //       }
      //       return null;
      //     })
      //     .filter((request) => request !== null)
      // );
      const sendRequests = await Promise.all(
        selectedRows
          .map(async (r) => {
            return {
              adminId,
              accademicId: academicYearFilter,
              fineMappedId: '', // use the key as termId
              dbResult: '',
              deletedId: 0,
            };
          })
          .filter((request) => request !== null)
      );
      const deleteResponse = await dispatch(DeleteFineMap(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: FineMapDeleteDataType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadFeeAllocationList(currentFeeAllocationListRequest);
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          setSelectedRows([]);
          setIsAllSelected(false);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }

        // setTermFeeData((prevDetails) =>
        //   prevDetails.filter((item: CreateTermFeeSettingTitleDataType) => !selectedRows.includes(item.termId))
        // );
      }
    }
  }, [confirm, dispatch, adminId, academicYearFilter, loadFeeAllocationList, currentFeeAllocationListRequest]);

  const FeeAllocationListColumns: DataTableColumn<{ feeId: number; feeTitle: string }>[] = [
    {
      name: '',
      renderHeader: () => {
        return (
          <Checkbox
            aria-label="this is All checkbox selected "
            checked={isAllSelected}
            indeterminate={selectedRows.length > 0 && selectedRows.length < basicFees.length}
            onChange={handleAllCheckboxChange}
          />
        );
      },
      renderCell: (row) => {
        return (
          <Checkbox
            aria-label="this is selected row wise checkbox selected "
            checked={selectedRows.includes(row.feeId)}
            onChange={() => handleRowCheckboxChange(row.feeId)}
          />
        );
      },
    },
    {
      name: 'fee',
      renderCell: (row) => {
        return (
          <Typography minWidth={200} variant="subtitle2">
            {row.feeTitle}
          </Typography>
        );
      },
      headerLabel: 'Fee Title',
    },
    ...termFees.map((term) => ({
      name: `${term.termId}`,
      headerLabel: `${term.termTitle}`,
      renderCell: (row) => {
        const { feeId } = row;
        const fineMappedListArray = fineMappedList.filter((f) => f.feeId === feeId);
        const fineMappedTermsArray = fineMappedListArray.flatMap((f) => f.fineMappedTerms || []);
        const Fines = fineMappedTermsArray.filter((f) => f.termId === term.termId).map((m) => m.fineId);
        const fineId2 = fineTypes.find((f) => f.fineId === Fines[0])?.fineId;

        // console.log('FineMappedId::::----', FineMappedId);
        // console.log('selectedRows::::----', selectedRows);
        if (Fines.length > 0) {
          // const { fineId } = Fines;
          const fineTitle = fineTypes.find((f) => f.fineId === Fines[0])?.title;
          const fineId = fineTypes.find((f) => f.fineId === Fines[0])?.fineId;
          return (
            <Box minWidth={200} height="100%">
              {!selectedRows.includes(row.feeId) ? (
                <Stack
                  sx={{ background: 'linear-gradient(90deg,rgba(255, 187, 99, 1) 50%, rgba(255, 219, 153, 1) 100%);' }}
                  px={1.5}
                  height="100%"
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography variant="subtitle1" fontSize={14}>
                    {fineTitle}
                  </Typography>
                  {/* {savedStatus[feeId] && savedStatus[feeId].includes(term.termId) && (
                      <Lottie animationData={paidSuccess} loop={false} style={{ width: '25px' }} />
                    )} */}
                  <Stack>
                    <IconButton onClick={() => handleDeleteCell(fineMappedTermsArray, term, fineTitle)} size="small">
                      <DeleteIcon sx={{ fontSize: '16px' }} />
                    </IconButton>
                  </Stack>
                </Stack>
              ) : (
                <Autocomplete
                  id="fine-type-autocomplete"
                  options={fineTypes}
                  getOptionLabel={(option) => option.title}
                  onChange={(event, newValue) => handleTypeChange(row.feeId, term.termId, newValue)}
                  sx={{ minWidth: 200 }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="standard"
                      sx={{
                        '.MuiInputBase-root': {
                          backgroundColor: theme.palette.warning.main,
                          borderRadius: 0,
                          height: 50,
                        },
                      }}
                    />
                  )}
                  defaultValue={fineTypes.find((item) => item.fineId === fineId)}
                />
              )}
            </Box>
          );
        }
        return (
          <Stack minWidth={200} height="100%">
            {/* <Autocomplete
              disablePortal
              id="fine-type-autocomplete"
              options={fineTypes}
              // value={fineTypes.find((item) => item.fineId === fineId2) || null}
              // isOptionEqualToValue={(option, value) => option.fineId === value.fineId}
              getOptionLabel={(option) => option.title || ''}
              onChange={(event, newValue) => {
                handleTypeChange(row.feeId, term.termId, newValue);
              }}
              sx={{ minWidth: 200 }}
              renderInput={(params) => (
                <TextField
                  variant="standard"
                  placeholder="Select"
                  {...params}
                  sx={{
                    '.MuiInputBase-root': {
                      backgroundColor: theme.palette.common.white,
                      borderRadius: 0,
                      height: 50,
                    },
                  }}
                />
              )}
              clearOnEscape
            /> */}
            <Autocomplete
              disablePortal
              id="fine-type-autocomplete"
              options={fineTypes}
              value={
                fineTypes.find((item) =>
                  fineMappedValues.some(
                    (mapped) =>
                      mapped.feeId === row.feeId && mapped.termId === term.termId && mapped.fineId === item.fineId
                  )
                ) || null
              }
              isOptionEqualToValue={(option, value) => option.fineId === value.fineId}
              getOptionLabel={(option) => option.title || ''}
              onChange={(event, newValue) => {
                handleTypeChange(row.feeId, term.termId, newValue);
              }}
              sx={{ minWidth: 200 }}
              renderInput={(params) => (
                <TextField
                  variant="standard"
                  placeholder="Select"
                  {...params}
                  sx={{
                    '.MuiInputBase-root': {
                      backgroundColor: theme.palette.common.white,
                      borderRadius: 0,
                      height: 50,
                    },
                  }}
                />
              )}
              clearOnEscape
            />
          </Stack>
        );
      },
    })),
  ];
  return (
    <FeeAllocationRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset}>
          <Grid pb={1} container spacing={2}>
            <Grid item lg={2} xs={12}>
              <FormControl fullWidth>
                <Typography variant="subtitle1" fontSize={12} color="GrayText">
                  Academic Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter?.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                >
                  <MenuItem value={0} sx={{ display: 'none' }}>
                    Select Year
                  </MenuItem>
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
              <FormControl fullWidth>
                <Typography variant="subtitle1" fontSize={12} color="GrayText">
                  Select Type
                </Typography>
                <Select
                  labelId="feeTypeFilter"
                  id="feeTypeFilterSelect"
                  value={feeTypeFilter.toString()}
                  onChange={handleFeeTypeChange}
                  placeholder="Select Year"
                >
                  <MenuItem sx={{ display: 'none' }} value={0}>
                    Select Type
                  </MenuItem>
                  {FEE_TYPE_ID_OPTIONS.map((opt) => (
                    <MenuItem key={opt.id} value={opt.id}>
                      {opt.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item lg={1} xs={12}>
              <Stack spacing={1} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button type="reset" variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Stack position="absolute" top={70} right={20}>
            {selectedRows.length > 0 && (
              <Tooltip title="Delete All">
                <IconButton
                  sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                  aria-label="delete"
                  color="error"
                  onClick={handleDeleteMultiple}
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        </form>
        <Paper
          sx={{
            border: basicFees?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
            // height: 'calc(100vh - 18.7rem)',
          }}
        >
          <Box
            sx={{
              height: '100%',
              width: { xs: basicFees?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            <DTVirtuoso
              hoverDisable={false}
              isSubmitting={isSubmitting}
              disabledCheckBox={disabledCheckBoxes}
              columns={FeeAllocationListColumns}
              data={basicFees}
              getRowKey={getRowKey}
              fetchStatus={ScholarshipFeeStatus}
              showHorizontalScroll
            />
          </Box>
        </Paper>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          {basicFees.length !== 0 && (
            <Stack spacing={2} direction="row">
              <Button disabled={isSubmitting} onClick={onClose} variant="contained" color="secondary">
                Cancel
              </Button>
              <LoadingButton
                startIcon={<SaveIcon />}
                fullWidth
                loadingPosition="start"
                onClick={() => {
                  handleSave();
                }}
                loading={isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </LoadingButton>
            </Stack>
          )}
        </Box>
      </Box>
    </FeeAllocationRoot>
  );
}

export default FeeAllocation;
