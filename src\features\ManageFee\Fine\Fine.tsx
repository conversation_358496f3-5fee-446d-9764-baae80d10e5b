/* eslint-disable no-lonely-if */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, ReactElement, useCallback, useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  RadioGroup,
  Select,
  MenuItem,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Radio,
  FormControlLabel,
  SelectChangeEvent,
  FormControl,
  IconButton,
  Checkbox,
  Tooltip,
  Collapse,
  Chip,
  FormHelperText,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import AddIcon from '@mui/icons-material/Add';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import LoadingButton from '@mui/lab/LoadingButton';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import {
  GetFineListRequestType,
  GetFineListDataType,
  CreateEditFineListRequestType,
  DeleteFineListResponseType,
  DeleteFineListRequestType,
} from '@/types/ManageFee';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getYearData } from '@/config/storeSelectors';
import { DeleteFineList, CreateEditFineList, fetchFineList } from '@/store/ManageFee/manageFee.thunks';
import CachedIcon from '@mui/icons-material/Cached';
import SearchIcon from '@mui/icons-material/Search';
import SearchOffIcon from '@mui/icons-material/SearchOff';
import NoData from '@/assets/no-datas.png';
import Person2Icon from '@mui/icons-material/Person2';
import { IoIosArrowUp } from 'react-icons/io';
import DeleteIcon from '@mui/icons-material/Delete';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import Popup from '@/components/shared/Popup/Popup';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import * as Yup from 'yup';
import { Formik, Form, Field } from 'formik';
import FeeAllocation from './FeeAllocation';
import { BiRupee } from 'react-icons/bi';

const FineRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const validationSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  type: Yup.number().oneOf([1, 2], 'Type is required'),
  value: Yup.number().typeError('Fine amount must be a number').required('Fine amount is required'),
  incrementDays: Yup.number().typeError('Increment days must be a number').required('Increment days are required'),
  growAmount: Yup.number().typeError('Grow amount must be a number').required('Grow amount is required'),
});

export default function Fine() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const YearData = useAppSelector(getYearData);
  const { confirm } = useConfirm();
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [titleFilter, setTitleFilter] = useState('');
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(true);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [individualRemoveRowButton, setIndividualRemoveRowButton] = useState<{ [key: string]: boolean }>({});
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [rowBackgroundColors, setRowBackgroundColors] = useState<Record<number, string>>({});
  const [editTableRow, setEditTableRow] = useState<Record<number, boolean>>({});
  const [rowSelect, setRowSelect] = useState<Record<string, boolean>>({});
  const [popup, setPopup] = useState(false);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);

  useEffect(() => {
    // Load fineValuesArray from local storage on component mount
    const storedFineValuesArray = localStorage.getItem('fineValuesArray');
    // if (storedfineValuesArray) {
    //   setFineValuesArray(JSON.parse(storedfineValuesArray));
    // }
    console.log('storedfineValuesArray:::-', storedFineValuesArray);
  }, []);
  const [fineList, setFineList] = useState<any[]>([
    {
      id: 0,
      fineId: 0,
      title: '',
      type: -1,
      value: '',
      growAmount: '',
      incrementDays: '',
      status: 1,
    },
  ]);
  const handleAddRow = () => {
    const newRow = {
      id: fineList.length + 1,
      fineId: 0,
      title: '',
      type: -1,
      value: '',
      growAmount: '',
      incrementDays: '',
      status: 1,
    };
    setFineList([...fineList, newRow]);
    setRowBackgroundColors((prev) => ({ ...prev, [newRow.fineId]: isLight ? '#f0fdf4' : theme.palette.grey[900] }));
    setIndividualSaveButtonEnabled((prev) => ({ ...prev, [newRow.fineId]: true }));
    setIndividualRemoveRowButton((prev) => ({ ...prev, [newRow.fineId]: true }));
    setEditTableRow((prev) => ({ ...prev, [newRow.fineId]: true }));
  };

  const handleTitleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: GetFineListDataType
  ) => {
    setFineList((prevRows) =>
      prevRows.map((row) => {
        if (row.fineId !== 0) {
          if (row.fineId === item.fineId) {
            return { ...row, title: event.target.value };
          }
        } else {
          if (row.id === item.id) {
            return { ...row, title: event.target.value };
          }
        }
        return row;
      })
    );
  };
  const handleFineAmountChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: GetFineListDataType
  ) => {
    const { value } = event.target;
    setFineList((prevRows) =>
      prevRows.map((row) => {
        if (row.fineId !== 0) {
          if (row.fineId === item.fineId) {
            return { ...row, value };
          }
        } else {
          if (row.id === item.id) {
            return { ...row, value };
          }
        }
        return row;
      })
    );
  };
  const handleGrowAmountChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: GetFineListDataType
  ) => {
    const { value } = event.target;
    setFineList((prevRows) =>
      prevRows.map((row) => {
        if (row.fineId !== 0) {
          if (row.fineId === item.fineId) {
            return { ...row, growAmount: value };
          }
        } else {
          if (row.id === item.id) {
            return { ...row, growAmount: value };
          }
        }
        return row;
      })
    );
  };
  const handleIncrementDaysChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    item: GetFineListDataType
  ) => {
    const { value } = event.target;
    setFineList((prevRows) =>
      prevRows.map((row) => {
        if (row.fineId !== 0) {
          if (row.fineId === item.fineId) {
            return { ...row, incrementDays: value };
          }
        } else {
          if (row.id === item.id) {
            return { ...row, incrementDays: value };
          }
        }
        return row;
      })
    );
  };
  // Handler to update the state when radio button value changes
  const handleStatusChange = (event: React.ChangeEvent<HTMLInputElement>, item: GetFineListDataType) => {
    const { value } = event.target;
    setFineList((prevRows) =>
      prevRows.map((row) => {
        if (row.fineId !== 0) {
          if (row.fineId === item.fineId) {
            return { ...row, status: Number(value) }; // Update status with the new value
          }
        } else {
          if (row.id === item.id) {
            return { ...row, status: Number(value) }; // Update status with the new value
          }
        }
        return row;
      })
    );
  };

  const handleDelete = (row: GetFineListDataType) => {
    setFineList((prev) => prev.filter((val) => !(val.id === row.id)));
  };

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  const initialFineListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      title: titleFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, titleFilter, feeTypeFilter]
  );
  const currentFineListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      title: titleFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, titleFilter, feeTypeFilter]
  );

  const loadFineList = useCallback(
    async (request: GetFineListRequestType) => {
      try {
        const data = await dispatch(fetchFineList(request)).unwrap();
        if (data) {
          setFineList(data);
          console.log('data::::----', data);
        }
      } catch (err) {
        console.error('Error loading Scholarship list:', err);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadFineList(initialFineListRequest);
  }, [dispatch, adminId, initialFineListRequest, loadFineList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFineList({ ...currentFineListRequest, academicId: parseInt(e.target.value, 10) });
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadFineList({ ...currentFineListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleSave = useCallback(
    async (row: GetFineListDataType) => {
      try {
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.fineId]: true }));
        const sendreq: CreateEditFineListRequestType = [
          {
            adminId,
            accademicId: academicYearFilter,
            fineId: row.fineId,
            title: row.title,
            type: row.type,
            value: row.value,
            growAmount: row.growAmount,
            incrementDays: row.incrementDays,
            status: row.status,
            feeTypeId: feeTypeFilter,
            dbResult: '',
            resultId: 0,
          },
        ];
        console.log('sendreq::::----', sendreq);
        const response = await dispatch(CreateEditFineList(sendreq)).unwrap();

        if (response) {
          setEditTableRow([]);
          loadFineList(initialFineListRequest);
          setIndividualSaveButtonEnabled((prev) => ({ ...prev, [row.fineId]: false }));
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.fineId]: false }));
          console.log('response::::----', response);
          const responseDbResult = response[0];

          const firstDbResult = responseDbResult?.dbResult;
          const isSuccess = responseDbResult?.dbResult === 'Success';
          const isUpdate = responseDbResult?.dbResult === 'Updated';

          console.log('firstDbResult::::----', firstDbResult);

          if (isSuccess) {
            await showConfirmation(
              <SuccessMessage icon="" jsonIcon={Success} loop={false} message="Fine Added successfully" />,
              ''
            );
          }

          if (isUpdate) {
            await showConfirmation(
              <SuccessMessage icon="" jsonIcon={Updated} loop={false} message="Fine Updated successfully" />,
              ''
            );
          }

          setFineList((prevList) => prevList.map((item) => (item.fineId === row.fineId ? { ...item, ...row } : item)));
          setRowBackgroundColors((prev) => ({
            ...prev,
            [row.fineId]: isLight ? theme.palette.common.white : theme.palette.grey[700],
          }));
        } else {
          await showConfirmation(
            <ErrorMessage icon="" jsonIcon={errorIcon} loop={false} message="Failed Please try again." />,
            ''
          );
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.fineId]: false }));
        }
      } catch (err) {
        await showConfirmation(
          <ErrorMessage icon="" jsonIcon={errorIcon} loop={false} message="Something went wrong Please try again." />,
          ''
        );
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.fineId]: false }));
        console.error(err);
      }
    },
    [
      academicYearFilter,
      adminId,
      feeTypeFilter,
      dispatch,
      showConfirmation,
      loadFineList,
      initialFineListRequest,
      isLight,
      theme,
    ]
  );

  const handleDeleteRow = useCallback(
    async (row: GetFineListDataType) => {
      const { fineId } = row;
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              `Are you sure you want to delete the row &quot;{row.title}&quot; ?`
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq: DeleteFineListRequestType = [
          { adminId, accademicId: academicYearFilter, fineId, dbResult: '', deletedId: 0 },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteFineList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteFineListResponseType[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          setSnackBar(true);
          setFineList((prevDetails) => prevDetails.filter((item) => item.fineId !== fineId));
          // setSelectedCards((prevSelectedCards) =>
          //   prevSelectedCards.filter((card) => card.messageId !== errorMessages?.messageId)
          // );
          // Filter out the deleted cards from messageTempListDatas
          // setMessageTempListDatas((prevMessageTempListDatas) =>
          //   prevMessageTempListDatas.filter((item: any) => !selectedCards.includes(item.messageId))
          // );
          // setLoadRefresh(false);
          // loadMessageTempList(currentMessageTempRequest);

          // setSelectedCards([]);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Scholarhip?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: any = await Promise.all(
        Object.keys(rowSelect)
          .map(async (key) => {
            if (rowSelect[key]) {
              return {
                adminId,
                accademicId: academicYearFilter,
                fineId: key, // use the key as termId
                dbResult: '',
                deletedId: 0,
              };
            }
            return null;
          })
          .filter((request) => request !== null)
      );
      const deleteResponse = await dispatch(DeleteFineList(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteFineListRequestType = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadFineList(initialFineListRequest);
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          setRowSelect({});
          setIsAllSelected(false);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }

        // setTermFeeData((prevDetails) =>
        //   prevDetails.filter((item: CreateTermFeeSettingTitleDataType) => !selectedRows.includes(item.termId))
        // );
      }
    }
  }, [confirm, dispatch, rowSelect, adminId, academicYearFilter, loadFineList, initialFineListRequest]);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setFeeTypeFilter(0);
      setTitleFilter('');
      loadFineList(initialFineListRequest);
    },
    [initialFineListRequest, loadFineList]
  );

  const handleEditCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>, fineId: number) => {
    const { checked } = event.target;

    if (checked) {
      setRowSelect((prev) => ({ ...prev, [fineId]: true }));
    } else {
      setRowSelect((prev) => ({ ...prev, [fineId]: false }));
    }
  };

  // const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const isChecked = event.target.checked;
  //   setIsAllSelected(isChecked);
  //   setRowSelect(fineList.reduce((acc, item) => ({ ...acc, [item.fineId]: isChecked }), {}));
  // };

  const handleSelectAll = (event: ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setIsAllSelected(isChecked);

    const selectedRows = isChecked
      ? fineList
          .filter((item) => item.fineId) // only include rows with fineId
          .reduce((acc, item) => {
            acc[item.fineId] = true;
            return acc;
          }, {} as Record<string, boolean>)
      : {};

    setRowSelect(selectedRows);
  };

  const action = (
    <Button color="secondary" size="small">
      UNDO
    </Button>
  );
  // Check if there is any scholarship with a different fineId in the fineList
  return (
    <Page title="Scholarship">
      <FineRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between" flexWrap="wrap" gap={1}>
            <Typography variant="h6" fontSize={17}>
              Fine Type Creation
            </Typography>
            <Stack direction="row" justifyContent="end" flex={1}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <SearchOffIcon />
                </IconButton>
              )}
            </Stack>
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="end"
              sx={{
                '@media (max-width:430px)': {
                  flex: 1,
                  paddingBottom: 1,
                },
              }}
            >
              {fineList.length > 0 && Object.values(rowSelect).some((checked) => checked) && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Stack>
                <Button
                  type="button"
                  color="primary"
                  variant="contained"
                  startIcon={<BiRupee />}
                  onClick={() => setPopup(true)}
                  sx={{ whiteSpace: 'nowrap' }}
                >
                  Map to Fees
                </Button>
              </Stack>
            </Stack>
          </Box>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container columnSpacing={2} rowSpacing={0.5} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Fine Title
                      </Typography>
                      <TextField
                        placeholder="Enter"
                        name="titleFilter"
                        value={titleFilter}
                        onChange={(e) => {
                          setTitleFilter(e.target.value);
                          loadFineList({
                            ...currentFineListRequest,
                            title: e.target.value,
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm="auto" lg={1}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px', mt: { xs: 1, sm: 0 } }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {academicYearFilter !== 0 && feeTypeFilter !== 0 && (
              <Box display="flex" sx={{ justifyContent: 'end', pb: 2, pt: showFilter ? 0 : 2 }}>
                <Button
                  size="small"
                  type="button"
                  color="secondary"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                >
                  Add Row
                </Button>
              </Box>
            )}
            {academicYearFilter !== 0 && feeTypeFilter !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  border: '1px solid #e8e8e9',
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    '&::-webkit-scrollbar': {
                      height: '15px',
                    },
                  }}
                >
                  <Table
                    sx={{
                      minWidth: {
                        xs: '1180px',
                      },
                    }}
                    stickyHeader
                    aria-label="simple table"
                  >
                    <TableHead>
                      <TableRow>
                        <TableCell width={50}>
                          <Checkbox
                            aria-label="All row select"
                            disabled={fineList.length === 0 || fineList.length === 1}
                            indeterminate={
                              fineList.length > 0 &&
                              Object.values(rowSelect).some((checked) => checked) &&
                              !isAllSelected
                            }
                            checked={isAllSelected}
                            color="primary"
                            onChange={handleSelectAll} // Add this function to handle select all
                          />
                        </TableCell>
                        <TableCell width={150}>Title</TableCell>
                        <TableCell width={150}>Type</TableCell>
                        <TableCell width={150}>Fine Amount</TableCell>
                        <TableCell width={150}>Grow Amount</TableCell>
                        <TableCell width={150}>Increment Days</TableCell>
                        <TableCell width={200}>Status</TableCell>
                        <TableCell width={150}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fineList?.map((row) => (
                        <Formik
                          key={row.scholarshipId}
                          initialValues={{
                            title: row.title || '',
                            type: row.type || '',
                            value: row.value || '',
                            incrementDays: row.incrementDays || '',
                            growAmount: row.growAmount || '',
                            // status: row.status || 1,
                          }}
                          validationSchema={validationSchema}
                          onSubmit={(values) => handleSave(row)}
                        >
                          {({ values, errors, touched, handleChange, handleBlur, handleSubmit }) => {
                            const fineExists = fineList.some((f) => f.fineId !== row.fineId);

                            // const fineExists = fineList.some((f) => f.fineId !== row.fineId);

                            return (
                              <TableRow
                                // sx={{ backgroundColor: rowBackgroundColors[row.fineId] }}
                                hover={!rowBackgroundColors[row.fineId]}
                                key={row.fineId}
                              >
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  <Checkbox
                                    aria-label="Individual row select"
                                    disabled={editTableRow[row.fineId]}
                                    color="primary"
                                    checked={rowSelect[row.fineId] || false}
                                    onChange={(event) => handleEditCheckboxChange(event, row.fineId)}
                                  />
                                </TableCell>
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    row.title
                                  ) : (
                                    <TextField
                                      name="title"
                                      onBlur={handleBlur}
                                      error={touched.title && Boolean(errors.title)}
                                      helperText={touched.title && errors.title}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleTitleChange(e, row);
                                      }}
                                      value={row.title}
                                      inputProps={{
                                        style: {
                                          padding: '5px',
                                          color:
                                            rowSelect[row.fineId] && !editTableRow[row.fineId]
                                              ? theme.palette.common.black
                                              : '',
                                        },
                                      }}
                                      variant="outlined"
                                      placeholder="Enter title"
                                    />
                                  )}
                                </TableCell>

                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    row.type === 1 ? (
                                      'Fixed'
                                    ) : row.type === 2 ? (
                                      'Variable'
                                    ) : (
                                      ''
                                    )
                                  ) : (
                                    <>
                                      <Select
                                        name="type"
                                        onBlur={handleBlur}
                                        error={touched.type && Boolean(errors.type)}
                                        displayEmpty
                                        value={row.type}
                                        onChange={(e) => {
                                          handleChange(e); // Pass the event to Formik
                                          setFineList((prevRows) =>
                                            prevRows.map((item) =>
                                              row.fineId === item.fineId
                                                ? { ...item, type: parseInt(e.target.value, 10) }
                                                : item
                                            )
                                          );
                                        }}
                                        sx={{
                                          height: '30.5px',
                                          width: 150,
                                          // borderRadius: 5,
                                          color: rowSelect[row.fineId] ? theme.palette.common.black : '',
                                        }}
                                        size="small"
                                      >
                                        <MenuItem sx={{ display: 'none' }} value={-1}>
                                          Select Type
                                        </MenuItem>
                                        <MenuItem value={1}>Fixed</MenuItem>
                                        <MenuItem value={2}>Variable</MenuItem>
                                      </Select>
                                      {touched.type && errors.type && (
                                        <FormHelperText sx={{ fontSize: 10, m: 0 }} error>
                                          {errors.type}
                                        </FormHelperText>
                                      )}
                                    </>
                                  )}
                                </TableCell>

                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    row.value
                                  ) : (
                                    <TextField
                                      sx={{ width: 150 }}
                                      type="number"
                                      name="value"
                                      onBlur={handleBlur}
                                      error={touched.value && Boolean(errors.value)}
                                      helperText={touched.value && errors.value}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleFineAmountChange(e, row);
                                      }}
                                      value={row.value}
                                      inputProps={{
                                        style: {
                                          padding: '5px',
                                          color: rowSelect[row.fineId] ? theme.palette.common.black : '',
                                        },
                                      }}
                                      variant="outlined"
                                      placeholder="Enter fine"
                                    />
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    row.incrementDays
                                  ) : (
                                    <TextField
                                      sx={{ width: 150 }}
                                      type="number"
                                      inputMode="decimal"
                                      name="incrementDays"
                                      onBlur={handleBlur}
                                      error={touched.incrementDays && Boolean(errors.incrementDays)}
                                      helperText={touched.incrementDays && errors.incrementDays}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleIncrementDaysChange(e, row);
                                      }}
                                      value={row.incrementDays}
                                      inputProps={{
                                        style: {
                                          padding: '5px',
                                          color: rowSelect[row.fineId] ? theme.palette.common.black : '',
                                        },
                                      }}
                                      placeholder="Enter grow"
                                      variant="outlined"
                                    />
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    row.growAmount
                                  ) : (
                                    <TextField
                                      sx={{ width: 150 }}
                                      type="number"
                                      name="growAmount"
                                      onBlur={handleBlur}
                                      error={touched.growAmount && Boolean(errors.growAmount)}
                                      helperText={touched.growAmount && errors.growAmount}
                                      FormHelperTextProps={{
                                        sx: { fontSize: 10, m: 0 },
                                      }}
                                      onChange={(e) => {
                                        handleChange(e);
                                        handleGrowAmountChange(e, row);
                                      }}
                                      value={row.growAmount}
                                      inputProps={{
                                        style: {
                                          padding: '5px',
                                          color: rowSelect[row.fineId] ? theme.palette.common.black : '',
                                        },
                                      }}
                                      placeholder="Enter day"
                                      variant="outlined"
                                    />
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  {!editTableRow[row.fineId] && !rowSelect[row.fineId] ? (
                                    <Chip
                                      size="small"
                                      label={` ${row.status === 1 ? 'Active' : row.status === 2 ? 'Inactive' : ''}`}
                                      variant="filled"
                                      color={row.status === 1 ? 'success' : 'error'}
                                    />
                                  ) : (
                                    <RadioGroup
                                      row
                                      aria-labelledby="demo-row-radio-buttons-group-label"
                                      name="row-radio-buttons-group"
                                      defaultValue={row.status}
                                      value={row.status}
                                      onChange={(e) => handleStatusChange(e, row)}
                                      sx={{ color: rowSelect[row.fineId] ? theme.palette.common.black : '' }}
                                    >
                                      <FormControlLabel
                                        control={
                                          <Radio
                                            sx={{ color: rowSelect[row.fineId] ? theme.palette.common.black : '' }}
                                            size="small"
                                            value={1}
                                          />
                                        }
                                        label="Active"
                                      />
                                      <FormControlLabel
                                        control={
                                          <Radio
                                            sx={{ color: rowSelect[row.fineId] ? theme.palette.common.black : '' }}
                                            size="small"
                                            value={0}
                                          />
                                        }
                                        label="Inactive"
                                      />
                                    </RadioGroup>
                                  )}
                                </TableCell>
                                <TableCell
                                  sx={{ backgroundColor: rowSelect[row.fineId] ? theme.palette.warning.light : '' }}
                                >
                                  <Stack direction="row" gap={1}>
                                    <LoadingButton
                                      loading={individualSaveLoading[row.fineId]}
                                      disabled={!rowSelect[row.fineId] && !individualSaveButtonEnabled[row.fineId]}
                                      variant="contained"
                                      size="small"
                                      color={!rowSelect[row.fineId] ? 'success' : 'warning'}
                                      sx={{ py: 0.5, fontSize: '10px' }}
                                      onClick={handleSubmit}
                                    >
                                      {rowSelect[row.fineId]
                                        ? 'Update'
                                        : !individualSaveButtonEnabled[row.fineId]
                                        ? 'Saved'
                                        : 'Save'}
                                    </LoadingButton>
                                    <Button
                                      variant="contained"
                                      size="small"
                                      color="error"
                                      sx={{ py: 0.5, fontSize: '10px' }}
                                      onClick={() =>
                                        individualRemoveRowButton[row.fineId] ? handleDelete(row) : handleDeleteRow(row)
                                      }
                                    >
                                      {editTableRow[row.fineId] && !rowSelect[row.fineId] ? 'Remove' : 'Delete'}
                                    </Button>
                                  </Stack>
                                </TableCell>
                              </TableRow>
                            );
                          }}
                        </Formik>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </FineRoot>
      <PositionedSnackbar
        content="1 Row Deleted"
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        open={snackBar}
        TransitionComponent="SlideTransition"
        onClose={() => setSnackBar(false)}
        autoHideDuration={5000}
        action={action}
      />
      <Popup
        size="xxl"
        title="Map Fine to Fees"
        state={popup}
        onClose={() => setPopup(false)}
        popupContent={
          <FeeAllocation
            academicYearId={academicYearFilter}
            feeTypeId={feeTypeFilter}
            onClose={() => setPopup(false)}
          />
        }
      />
    </Page>
  );
}
