import { FetchStatus } from './Common';
import { GetReceiptForPrintType } from './ManageFee';

export type PaymentOrderItem = {
  feeid: number;
  termid: number;
  totalamount: number;
  scholarship: number;
  discount: number;
  fine: number;
  payableamount: number;
};

export type PaymentDeviceType = 'Web' | 'Android' | 'iOS';

export type PaymentOrderRequest = {
  academicid: number | undefined;
  sectionid: number | undefined;
  classid: number | undefined;
  studentid: number | undefined;
  feetypeid: number | undefined;
  devicetype: PaymentDeviceType;
  totalamount: number;
  payableamount: number;
  items: Array<PaymentOrderItem>;
  user_agent: string;
  browser_tz: string;
  browser_color_depth: string;
  browser_java_enabled: string;
  browser_screen_height: string;
  browser_screen_width: string;
  browser_language: string;
  additional_info1: string;
  additional_info2: string;
};

export type PaymentOrderResponse = {
  receiptId: number;
  receiptNumber: number;
  studentId: number;
  launchUrl: string;
  bdOrderId: string;
  merchantId: string;
  rData: string;
};

export type PaymentOrderAPIResponse = {
  status: string;
  data: PaymentOrderResponse;
};

export type TransactionResponseRequest = {
  studentId: number;
  tr: string | null;
};

export type TransactionAPIResponseData = {
  transactionId: string;
  orderNumber: string;
  transactionDate: string;
  receiptNo: number;
  amount: number;
  returnUrl: string;
  transactionCode: string;
  transactionStatus: string;
  transactionDesc: string;
  paymentMethod: string;
  redirectUrl: string | undefined;
};

export type TransactionAPIResponse = {
  status: string;
  data: TransactionAPIResponseData;
};

// Get Parent Pay Fee
export type StudentDetailsType = {
  academicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeTypeId: number;
};

export type FeeDetailsType = {
  particularId: number;
  particularTitle: string;
  totalAmount: number;
  payableAmount: number;
};

export type TermFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  termId: number;
  termTitle: string;
  particularId: number;
  totalAmount: number;
  scholarship: number;
  discount: number;
  fine: number;
  payableAmount: number;
};

export type GetParentPayFeeDataType = {
  studentDetails: StudentDetailsType;
  feeDetails: FeeDetailsType;
  termFeeDetails: TermFeeDetailsType;
};

export type GetcheckLoginType = {
  username: string;
  password: string;
  schoolCode: string;
};

// Payment Parent side
export type ParentPaymentState = {
  parentPayFee: {
    status: FetchStatus;
    data: GetParentPayFeeDataType[];
    error: string | null;
  };
  receiptOnlineList: {
    status: FetchStatus;
    data: GetReceiptForPrintType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

// Mswipe
export type GenerateTokenAPIResponse = {
  success: boolean;
  message: string;
  token: string;
};

// export type InitiatePaymentAPIRequest = {
//   amount: number;
//   mobileno: string;
//   email_id: string;
//   sessiontoken: string;
// };

// export type InitiatePaymentAPIResponse = {
//   success: boolean;
//   message: string;
//   txn_id: string;
//   payment_link: string;
// };

export type PaymentOrderResponseNew = {
  receiptId: number;
  receiptNumber: number;
  orderNumber: string;
  studentId: number;
  launchUrl: string;
  bdOrderId: string;
  merchantId: string;
  rData: string;
};

export type PaymentOrderRequestNew = {
  academicid: number | undefined;
  sectionid: number | undefined;
  classid: number | undefined;
  studentid: number | undefined;
  feetypeid: number | undefined;
  devicetype: PaymentDeviceType;
  totalamount: number;
  payableamount: number;
  items: Array<PaymentOrderItem>;
  user_agent: string;
  browser_tz: string;
  browser_color_depth: string;
  browser_java_enabled: string;
  browser_screen_height: string;
  browser_screen_width: string;
  browser_language: string;
  additional_info1: string;
  additional_info2: string;
  token: string;
};

export type PaymentOrderAPIResponseNew = {
  success: boolean;
  message: string;
  data: PaymentOrderResponseNew;
};

export type TransactionStatusRequest = {
  id: string;
  user_Agent: string;
};

export type TransactionStatusAPIResponse = {
  success: string;
  message: string;
};
