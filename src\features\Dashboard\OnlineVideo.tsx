/* eslint-disable react/no-unescaped-entities */
import Videoplayer from '@/components/Dashboard/Videoplayer';
import { Box, Card, CardMedia, IconButton, Stack, Typography } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Autoplay, Navigation, Pagination } from 'swiper';
import typography from '@/theme/typography';
// import { delay } from '@reduxjs/toolkit/dist/utils';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { getClassData, getdashboardVideosData, getdashboardVideosStatus } from '@/config/storeSelectors';
import { fetchClassList, fetchDashboardVideos } from '@/store/Dashboard/dashboard.thunks';
import React, { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import videoDummy from '@/assets/video-dummy.jpg';
import onlineClassBg from '@/assets/onlineClassBg.jpeg';
import noVideo from '@/assets/no-video.png';

import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import Popup from '@/components/shared/Popup/Popup';
import PopupVideoPlayer from '@/components/shared/PopupVideoPlayer';
// Install Swiper modules
// SwiperCore.use([Autoplay, Pagination]);

const OnlineVideoRoot = styled.div`
  max-height: 100%;
  max-width: 100%;
  margin-top: 1rem;
  margin-right: 0.5rem;

  @media screen and (max-width: 1199.5px) {
    margin: 0rem 0.5rem 1rem 0.5rem;
  }
  .swiper-pagination-onlinevideo {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    /* --swiper-pagination-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[100]};
    --swiper-pagination-bullet-inactive-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[100]}; */
  }
  .swiper_container_onlinevideo {
    position: relative;
  }
  .swiper-slide {
    position: relative;
  }
  .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.primary.main};
  }
  .swiper-button-prev {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.primary.main};
    transform: translateX(-8px);
  }

  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.primary.main};
    transform: translateX(8px);
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(5px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .darkmode-color {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }
  .MuiSelect-root {
    /* border: 1px solid red; */
    /* border-radius: 50px; */
    padding: 0px 10px 0px 10px;
    /* height: 5px; */
  }
`;

function OnlineVideo() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const [videoPopup, setVideoPopup] = React.useState(false);
  const [videoFile, setVideoFile] = React.useState(false);

  const handlePlayVideo = (eventLinkFile) => {
    setVideoPopup(true);
    setVideoFile(eventLinkFile);
  };

  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const handleVideoPlay = () => {
    setIsVideoPlaying(true);
  };

  const handleVideoEnd = () => {
    setIsVideoPlaying(false);
  };
  const dashboardVideosStatus = useAppSelector(getdashboardVideosStatus);
  const dashboardVideosData = useAppSelector(getdashboardVideosData);
  const ClassData = useAppSelector(getClassData);

  const AllClassOption: ClassListInfo = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState<ClassListInfo>(classDataWithAllClass[0]);

  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
      setIsVideoPlaying(false);
    }
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchDashboardVideos({ adminId, classId }));
    setIsVideoPlaying(false);
  }, [dispatch, adminId, classId]);

  return (
    <OnlineVideoRoot>
      <Card sx={{ padding: '1rem 0rem', height: '340px' }} className="darkmode-color">
        <Box>
          <Box
            sx={{ p: '0rem 1rem' }}
            display="flex"
            // justifyContent="space-between"
            alignItems="center"
            flexWrap="wrap"
          >
            <Typography variant="h6" fontSize={18} sx={{ fontFamily: typography.fontFamily }} textAlign="center">
              Online Video
            </Typography>
            <Stack sx={{ display: 'flex', alignItems: 'flex-end', flex: 1 }}>
              <Select
                sx={{
                  //  backgroundColor: theme.palette.grey[100], color: theme.palette.primary.main,
                  height: 30,
                }}
                value={className}
                onChange={handleChange}
                displayEmpty
                labelId="demo-dialog-select-label"
                id="demo-dialog-select"
                inputProps={{ 'aria-label': 'Without label' }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: '250px', // Adjust the value to your desired height
                    },
                  },
                }}
              >
                {classDataWithAllClass?.map((item: ClassListInfo) => (
                  <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                    {item.className}
                  </MenuItem>
                ))}
              </Select>
            </Stack>
          </Box>
          <Swiper
            spaceBetween={17}
            effect="coverflow"
            grabCursor
            coverflowEffect={{
              rotate: 0,
              depth: 0,
              stretch: 0,
            }}
            pagination={{ el: '.swiper-pagination-onlinevideo', clickable: true }}
            navigation={{
              nextEl: '.swiper-button-next',
              prevEl: '.swiper-button-prev',
            }}
            autoplay={{ delay: isVideoPlaying ? 100000 : 5000, disableOnInteraction: isVideoPlaying }}
            // loop
            modules={[Autoplay, Pagination, Navigation]}
            className="swiper_container_onlinevideo mt-4 "
          >
            {dashboardVideosStatus === 'loading' || dashboardVideosData.length === 0 ? (
              <SwiperSlide className="swiper-slide " onClick={handleVideoPlay}>
                <Stack spacing={2} m="10px" justifyContent="center" alignItems="center">
                  <img src={noVideo} alt="video" width="170px" />
                  <Typography variant="subtitle2" color="GrayText">
                    No Videos in this Class
                  </Typography>
                </Stack>
              </SwiperSlide>
            ) : (
              dashboardVideosData?.map((item) => (
                <SwiperSlide className="swiper-slide " key={item.youtubeId} onClick={handleVideoPlay}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    <CardMedia
                      image={onlineClassBg}
                      sx={{
                        borderRadius: 4,
                        margin: '10px',
                        height: '200px',
                        boxShadow: '0',
                        width: '240px',
                        backgroundColor: theme.palette.grey[400],
                        cursor: item.youtubeLink && 'pointer',
                      }}
                      onClick={() => handlePlayVideo(item.youtubeLink)}
                    >
                      <IconButton
                        onClick={() => handlePlayVideo(item.youtubeLink)}
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          color: theme.palette.primary.main,
                          fontSize: '3rem', // Adjust icon size as needed
                        }}
                        aria-label="play"
                      >
                        <BsFillPlayCircleFill
                        //  color={theme.palette.primary.main}
                        />
                      </IconButton>
                      {/* <Videoplayer
                          url={`https://www.youtube.com/watch?${item.youtubeLink}`}
                          onPlay={handleVideoPlay}
                          onEnd={handleVideoEnd}
                        /> */}
                    </CardMedia>
                  </Box>
                </SwiperSlide>
              ))
            )}
            <div className="slider-controler ">
              <div className="swiper-pagination-onlinevideo"> </div>
              <div className="d-none d-sm-flex">
                <KeyboardArrowLeftIcon className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle" />
              </div>
              <div className="d-none d-sm-flex">
                <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 shadow card rounded-circle" />
              </div>
            </div>
          </Swiper>
        </Box>
      </Card>
      <Popup
        title="Online Video"
        size="xs"
        state={videoPopup}
        onClose={() => setVideoPopup(false)}
        popupContent={<PopupVideoPlayer videoFile={videoFile} />}
      />
    </OnlineVideoRoot>
  );
}

export default OnlineVideo;
