/* eslint-disable react/no-unescaped-entities */
import Donutchart from '@/components/Dashboard/Donutchart';
import { Box, Card, Skeleton, Stack, Typography, FormControl } from '@mui/material';

import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import { donutChartOption } from '@/config/Charts';
import typography from '@/theme/typography';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassData,
  // getClassStatus,
  getdashboardAttendanceData,
  getdashboardAttendanceStatus,
} from '@/config/storeSelectors';
import { fetchClassList, fetchDashboardAttendance } from '@/store/Dashboard/dashboard.thunks';
import { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import useSettings from '@/hooks/useSettings';

const GraphRoot = styled.div`
  max-height: 100%;
  max-width: 100%;
  margin-top: 1rem;
  margin-right: 0.5rem;

  @media screen and (max-width: 1199.5px) {
    margin: 0rem 0.5rem 1rem 0.5rem;
  }
  .darkmode-color {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }
  .css-7lkbpc-MuiPaper-root-MuiPopover-paper-MuiMenu-paper {
    height: 10px;
  }
`;

function Graph() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const dashboardAttendanceStatus = useAppSelector(getdashboardAttendanceStatus);
  const dashboardAttendanceData = useAppSelector(getdashboardAttendanceData);
  const firstEntry = dashboardAttendanceData && dashboardAttendanceData[0];
  // const ClassStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchDashboardAttendance({ adminId, classId }));
  }, [dispatch, adminId, classId]);

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };
  return (
    <GraphRoot>
      <Card elevation={5} sx={{ padding: '1rem', height: '340px' }} className="darkmode-color">
        <Box
          display="flex"
          // justifyContent={{ xs: 'space-between' }}
          alignItems="center"
          flexWrap="wrap"
          mb={2}
        >
          <Typography variant="h6" fontSize={18} sx={{ fontFamily: typography.fontFamily }} textAlign="center">
            Today's Attendance
          </Typography>
          {/* <SelectBox Selection_Options={getClassListData} placeholder="Class" /> */}
          <Stack sx={{ display: 'flex', alignItems: 'flex-end', flex: 1 }}>
            <Select
              sx={{
                // backgroundColor: theme.palette.grey[100],
                // color: theme.palette.primary.main,
                height: 30,
                width: 'fit-content',
              }}
              value={className}
              onChange={handleChange}
              displayEmpty
              labelId="demo-dialog-select-label"
              id="demo-dialog-select"
              inputProps={{ 'aria-label': 'Without label' }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: '250px', // Adjust the value to your desired height
                  },
                },
              }}
            >
              {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}

              {classDataWithAllClass?.map((item: ClassListInfo) => (
                <MenuItem
                  sx={{
                    fontSize: '13px',
                  }}
                  key={item.classId}
                  value={item.className}
                >
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Box>
        {dashboardAttendanceStatus === 'loading' ? (
          <Stack alignItems="center" width="100%" position="relative">
            <Skeleton variant="circular" width={180} height={180} />
            <Skeleton
              variant="circular"
              width={125}
              height={125}
              sx={{
                position: 'absolute',
                top: 27,
                backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
              }}
            />
            <Skeleton
              // variant="circular"
              width={70}
              height={40}
              sx={{ position: 'absolute', top: 50 }}
            />
            <Skeleton
              // variant="circular"
              width={35}
              height={30}
              sx={{ position: 'absolute', top: 90 }}
            />
            <Stack direction="row" mt={3.5} spacing={3}>
              <Skeleton
                // variant="circular"
                width={50}
                height={20}
              />
              <Skeleton
                // variant="circular"
                width={50}
                height={20}
              />
              <Skeleton
                // variant="circular"
                width={50}
                height={20}
              />
            </Stack>
          </Stack>
        ) : (
          firstEntry && (
            <Donutchart
              chartData={[firstEntry.presentStudents, firstEntry.absentStudents, firstEntry.nottakenStudents]}
              chartOption={donutChartOption}
            />
          )
        )}
      </Card>
    </GraphRoot>
  );
}

export default Graph;
