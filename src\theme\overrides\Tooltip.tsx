import { Components, Theme } from '@mui/material';

export default function Tooltip(theme: Theme): Components<Theme> {
  const isLight = theme.palette.mode === 'light';

  return {
    MuiTooltip: {
      defaultProps: {
        PopperProps: {
          style: { zIndex: 99999 }, // inline style on the Popper element
        },
      },
      styleOverrides: {
        tooltip: {
          backgroundColor: theme.palette.grey[isLight ? 300 : 100],
          color: theme.palette.grey[isLight ? 900 : 900],
          maxHeight: '200px',
          overflow: 'auto',
        },
        arrow: {
          color: theme.palette.grey[isLight ? 600 : 900],
        },
      },
    },
  };
}
