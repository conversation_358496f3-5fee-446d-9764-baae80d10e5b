import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Grid,
  TextField,
  Box,
  Typography,
  Stack,
  Button,
  Card,
  Divider,
  FormControl,
  Autocomplete,
} from '@mui/material';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ClassListRequest } from '@/types/AcademicManagement';
import { fetchClassList } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { getClassListData, getClassListPageInfo, getClassListStatus, getSubmitting } from '@/config/storeSelectors';
import { YEAR_SELECT } from '@/config/Selection';
// import { ListClassmarking } from '@/features/AttendanceMarking/Classmarking/ListClassmarking';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';

const ClassMarkingRoot = styled.div`
  padding: 1rem;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
  h6 {
    font-weight: 600;
  }
  .sub-heading {
    font-size: 0.75rem;
    color: ${(props) => props.theme.palette.grey[600]};
    font-weight: 500;
  }
  .MuiAutocomplete-input {
  }
  .MuiCard-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
  }
  .Card {
    min-height: calc(100vh - 163px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .CardContainer {
  }
`;

export type ClassMarkingInfo = {
  Id: number;
  classMarkingYear: string;
  classMarkingClass: string;
  classMarkingRollNo: string;
  classMarkingDate: string;
};

const DefaultClassMarkingInfo: ClassMarkingInfo = {
  Id: 0,
  classMarkingYear: '',
  classMarkingClass: '',
  classMarkingRollNo: '',
  classMarkingDate: '',
};

function ClassMarking() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);

  const toggleDrawerClose = () => setDrawerOpen(false);

  const ClassMarkingValidationSchema = Yup.object({
    classMarkingClass: Yup.string().required('Please enter Class '),
    classMarkingYear: Yup.string().required('Please enter Year'),
    classMarkingRollNo: Yup.string().required('Please enter Roll number'),
  });

  const {
    values: { classMarkingYear, classMarkingClass, classMarkingRollNo },
    handleChange,
    handleSubmit,
    resetForm,
    touched,
    errors,
  } = useFormik<ClassMarkingInfo>({
    initialValues: DefaultClassMarkingInfo,
    validationSchema: ClassMarkingValidationSchema,
    onSubmit: (values) => {
      console.log('values', values);
    },
  });

  const isSubmitting = useAppSelector(getSubmitting);
  const classListStatus = useAppSelector(getClassListStatus);
  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  const paginationInfo = useAppSelector(getClassListPageInfo);
  const { pagenumber } = paginationInfo;

  const listClasses = useAppSelector(getClassListData).map((cls) => cls.className);

  const loadClassList = useCallback(
    (request: ClassListRequest) => {
      dispatch(fetchClassList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (classListStatus === 'idle') {
      loadClassList({ pageNumber: pagenumber, pageSize: 200 });
    }
  }, [loadClassList, pagenumber]);

  return (
    <Page title="Class Marking">
      <ClassMarkingRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Class Marking
          </Typography>
          <Divider />
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <Grid className="CardContainer" container py={2} spacing={3}>
              <Grid item lg={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Select Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    onChange={handleChange}
                    disabled={isSubmitting}
                    renderInput={(params) => (
                      <TextField
                        error={touched.classMarkingYear && !!errors.classMarkingYear}
                        value={classMarkingYear}
                        {...params}
                        placeholder="Select"
                        InputProps={{
                          ...params.InputProps,
                          sx: {
                            '& .MuiInputBase-input': {
                              height: '22px',
                              padding: '0 8px',
                              boxSizing: 'border-box',
                            },
                          },
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Select Class
                  </Typography>
                  <Autocomplete
                    options={listClasses}
                    disabled={isSubmitting}
                    renderInput={(params) => (
                      <TextField
                        name="classMarkingClass"
                        value={classMarkingClass}
                        onChange={handleChange}
                        error={touched.classMarkingClass && !!errors.classMarkingClass}
                        {...params}
                        placeholder="Select"
                         InputProps={{
                          ...params.InputProps,
                          sx: {
                            '& .MuiInputBase-input': {
                              height: '22px',
                              padding: '0 8px',
                              boxSizing: 'border-box',
                            },
                          },
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Date
                  </Typography>
                  <DateSelect />
                </FormControl>
              </Grid>
              <Grid item lg={12} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Absentees
                  </Typography>
                  <Typography className="sub-heading" variant="body2">
                    Please enter roll numbers of absentees using separators.
                  </Typography>
                  <TextField
                    name="classMarkingRollNo"
                    value={classMarkingRollNo}
                    onChange={handleChange}
                    error={touched.classMarkingRollNo && !!errors.classMarkingRollNo}
                    disabled={isSubmitting}
                    fullWidth
                    placeholder="Eg: 1,8,10,35..."
                  />
                </FormControl>
              </Grid>
              <Grid item lg={12} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Student Name
                  </Typography>
                  <TextField
                    multiline
                    fullWidth
                    minRows={3}
                    disabled
                    InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
                    name="classDescription"
                    placeholder="Eg: Alex,Jack,Micheal..."
                  />
                </FormControl>
              </Grid>
            </Grid>
            <Box
              pt={3}
              display="flex"
              sx={{ justifyContent: { xs: 'center', md: 'end' }, pr: { lg: '5' }, alignItems: 'end' }}
            >
              <Stack spacing={2} direction="row">
                <Button
                  onClick={handleReset}
                  variant="contained"
                  color="secondary"
                  sx={{ fontFamily: typography.fontFamily }}
                >
                  Cancel
                </Button>
                <LoadingButton
                  loading={isSubmitting}
                  fullWidth
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  Mark Absentees
                </LoadingButton>
              </Stack>
            </Box>
          </form>
        </Card>
      </ClassMarkingRoot>
      {/* <ListClassmarking onClose={toggleDrawerClose} open={drawerOpen} /> */}
      <Popup
        size="xs"
        state={drawerOpen}
        onClose={toggleDrawerClose}
        popupContent={<SuccessMessage message=" Passed Absentees Marked Successfully" />}
      />
    </Page>
  );
}

export default ClassMarking;
