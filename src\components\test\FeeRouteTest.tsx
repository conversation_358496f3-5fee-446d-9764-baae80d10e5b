import React from 'react';
import { useLocation, useParams, useOutletContext } from 'react-router-dom';
import { Box, Typography, Paper, Button, Stack } from '@mui/material';
import { useFeeGroupParams } from '@/hooks/useFeeGroupParams';
import { useFeeRouteHistory } from '@/hooks/useFeeRouteHistory';
import { useFeeFormState } from '@/hooks/useFeeRouteState';
import { FeeManagementOutletContext } from '@/pages/ManageFee';

interface TestFormData {
  testField: string;
  anotherField: number;
}

/**
 * Test component to validate nested routing implementation
 * This component can be temporarily added to any fee route for testing
 */
export function FeeRouteTest() {
  const location = useLocation();
  const params = useParams();
  const context = useOutletContext<FeeManagementOutletContext>();
  
  // Test fee group params hook
  const {
    feeGroupId,
    isEditMode,
    isCreateMode,
    navigateToEdit,
    navigateToCreate,
    navigateToList,
  } = useFeeGroupParams();

  // Test route history hook
  const { navigateWithHistory, goBack, canGoBack } = useFeeRouteHistory();

  // Test form state hook
  const {
    formData,
    updateField,
    updateFields,
    resetForm,
    hasFormData,
  } = useFeeFormState<TestFormData>('test-form', {
    testField: '',
    anotherField: 0,
  });

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Fee Route Test Component
      </Typography>
      
      <Stack spacing={3}>
        {/* Current Route Info */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Current Route Information
          </Typography>
          <Typography variant="body2">
            <strong>Pathname:</strong> {location.pathname}
          </Typography>
          <Typography variant="body2">
            <strong>Search:</strong> {location.search || 'None'}
          </Typography>
          <Typography variant="body2">
            <strong>Hash:</strong> {location.hash || 'None'}
          </Typography>
          <Typography variant="body2">
            <strong>State:</strong> {JSON.stringify(location.state) || 'None'}
          </Typography>
        </Paper>

        {/* Route Parameters */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Route Parameters
          </Typography>
          <Typography variant="body2">
            <strong>All Params:</strong> {JSON.stringify(params)}
          </Typography>
          <Typography variant="body2">
            <strong>Fee Group ID:</strong> {feeGroupId || 'None'}
          </Typography>
          <Typography variant="body2">
            <strong>Mode:</strong> {isEditMode ? 'Edit' : isCreateMode ? 'Create' : 'Unknown'}
          </Typography>
        </Paper>

        {/* Context Information */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Outlet Context
          </Typography>
          <Typography variant="body2">
            <strong>Top Bar Height:</strong> {context?.topBarHeight}px
          </Typography>
          <Typography variant="body2">
            <strong>Pay View:</strong> {context?.payView}
          </Typography>
          <Typography variant="body2">
            <strong>Can Go Back:</strong> {canGoBack() ? 'Yes' : 'No'}
          </Typography>
        </Paper>

        {/* Form State Test */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Form State Test
          </Typography>
          <Typography variant="body2" gutterBottom>
            <strong>Has Form Data:</strong> {hasFormData() ? 'Yes' : 'No'}
          </Typography>
          <Typography variant="body2" gutterBottom>
            <strong>Form Data:</strong> {JSON.stringify(formData)}
          </Typography>
          <Stack direction="row" spacing={1} mt={1}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => updateField('testField', 'Test Value')}
            >
              Set Test Field
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={() => updateField('anotherField', Math.floor(Math.random() * 100))}
            >
              Set Random Number
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={resetForm}
            >
              Reset Form
            </Button>
          </Stack>
        </Paper>

        {/* Navigation Test */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Navigation Test
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            <Button
              size="small"
              variant="contained"
              onClick={() => navigateToEdit('test-123')}
              disabled={!feeGroupId}
            >
              Navigate to Edit (test-123)
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={navigateToCreate}
            >
              Navigate to Create
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={navigateToList}
            >
              Navigate to List
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={goBack}
              disabled={!canGoBack()}
            >
              Go Back
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={() => navigateWithHistory('/manage-fee/overview')}
            >
              Go to Overview
            </Button>
          </Stack>
        </Paper>

        {/* Direct URL Test */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Direct URL Test Instructions
          </Typography>
          <Typography variant="body2" component="div">
            Try navigating directly to these URLs:
            <ul>
              <li>/manage-fee/fee-settings/fee-amount</li>
              <li>/manage-fee/fee-settings/fee-amount/123</li>
              <li>/manage-fee/fee-lists/basic</li>
              <li>/manage-fee/fee-lists/term/456</li>
            </ul>
            The routing should work correctly and preserve state.
          </Typography>
        </Paper>
      </Stack>
    </Box>
  );
}

export default FeeRouteTest;
