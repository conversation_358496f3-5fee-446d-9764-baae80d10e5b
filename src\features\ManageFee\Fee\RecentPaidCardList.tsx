/* eslint-disable no-nested-ternary */
import React, { useMemo } from 'react';
import {
  Paper,
  Stack,
  Typography,
  Avatar,
  Chip,
  Box,
  Grid,
  Snackbar,
  Divider,
  IconButton,
  Skeleton,
} from '@mui/material';
import PrintIcon from '@mui/icons-material/Print';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import Button from '@mui/material/Button';
import dayjs from 'dayjs';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import MuiAlert from '@mui/material/Alert';
import NoData from '@/assets/no-datas.png';
import { GetFeeOverviewPaidListType } from '@/types/ManageFee';
import UndoIcon from '@mui/icons-material/Undo';
import CancelIcon from '@mui/icons-material/Cancel';
import BlockIcon from '@mui/icons-material/Block';
import { CloseIcon, SuccessIcon, WarningIcon } from '@/theme/overrides/CustomIcons';
import { getStudentListData, getStudentListStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import man from '@/assets/man.png';
import woman from '@/assets/woman.png';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PendingIcon from '@mui/icons-material/Pending';

export function RecentPaidCardList({ data, onPrint, onUnpay, onApprove, onPdf, theme }: any) {
  const StudentListStatus = useAppSelector(getStudentListStatus);
  const StudentListData = useAppSelector(getStudentListData);
  const [snackbarOpen, setSnackbarOpen] = React.useState(false);
  const [snackbarMsg, setSnackbarMsg] = React.useState('');

  const handleCopy = (value: string, label: string) => {
    navigator.clipboard.writeText(value);
    setSnackbarMsg(`${label} copied!`);
    setSnackbarOpen(true);
  };

  type StatusType = 'Payment Initiated' | 'Paid' | 'Failed' | 'Cancelled' | 'Pending';

  const statusConfig = useMemo(
    () =>
      ({
        'Payment Initiated': { icon: <WarningIcon />, color: 'warning' },
        Paid: { icon: <SuccessIcon />, color: 'success' },
        Failed: { icon: <ErrorIcon />, color: 'error' },
        Cancelled: { icon: <CancelIcon />, color: 'error' },
        Pending: { icon: <PendingIcon />, color: 'warning' },
      } as const),
    []
  );

  return (
    <>
      {StudentListStatus === 'loading' ? (
        <Grid container spacing={2} mb={3} px={0.5}>
          {[...Array(6)].map((_, index) => (
            <Grid item xs={12} sm={12} md={12} lg={6} xl={4} key={index}>
              <Paper
                sx={{
                  border: 1,
                  borderColor: theme.palette.grey[200],
                  mt: 2,
                  borderRadius: 2,
                  // boxShadow: 1,
                  p: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                }}
              >
                <Stack direction="row" gap={2}>
                  <Skeleton variant="circular" width={40} height={40} />
                  <Stack flex={1}>
                    <Skeleton width="60%" height={20} />
                    <Skeleton width="40%" height={15} />
                  </Stack>
                  <Skeleton variant="rounded" width={40} height={15} />
                </Stack>
                <Skeleton width="90%" height={15} />
                <Skeleton width="80%" height={15} />
                <Skeleton width="75%" height={15} />
                <Stack direction="row" gap={1} mt={1}>
                  <Skeleton variant="rounded" width="100%" height={36} />
                  <Skeleton variant="rounded" width="100%" height={36} />
                  <Skeleton variant="rounded" width="100%" height={36} />
                </Stack>
              </Paper>
            </Grid>
          ))}
        </Grid>
      ) : data.length === 0 ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          height={{ xs: '100%', sm: '100%', lg: 465 }}
        >
          <Stack direction="column" alignItems="center">
            <img src={NoData} width="150px" alt="" />
            <Typography variant="subtitle2" mt={2} color="GrayText">
              No data found!
            </Typography>
          </Stack>
        </Box>
      ) : (
        <Grid container spacing={2} mb={3}>
          {data.map((row: GetFeeOverviewPaidListType) => {
            const matchedStudents =
              StudentListData?.filter((student: any) => student.studentId === row.studentId) || [];
            // Take the first matched student's gender (if any)
            // const studentGender = matchedStudents[0]?.studentGender;

            const config = statusConfig[row.status as StatusType] ?? {
              icon: <HourglassEmptyIcon />,
              color: 'secondary' as const,
            };

            return (
              <Grid item xs={12} sm={12} md={12} lg={6} xl={4} xxl={3} key={row.receiptId}>
                <Paper
                  sx={{
                    // p: 2,
                    mt: 2,
                    // border: `1px solid ${theme.palette.grey[300]}`,
                    borderRadius: 2,
                    // boxShadow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    height: '100%',
                    // overflow: 'hidden',
                    position: 'relative',
                    // borderTopLeftRadius: 30,
                    border: 1,
                    borderColor:
                      row.status === 'Payment Initiated'
                        ? theme.palette.warning.lighter
                        : row.status === 'Paid'
                        ? theme.palette.success.lighter
                        : row.status === 'Failed'
                        ? theme.palette.error.lighter
                        : row.status === 'Cancelled'
                        ? theme.palette.error.lighter
                        : row.status === 'Pending'
                        ? theme.palette.warning.lighter
                        : theme.palette.secondary.lighter,
                    // bgcolor:
                    //   row.status === 'Payment Initiated'
                    //     ? theme.palette.warning.lighter
                    //     : row.status === 'Paid'
                    //     ? theme.palette.success.lighter
                    //     : row.status === 'Failed'
                    //     ? theme.palette.error.lighter
                    //     : row.status === 'Cancelled'
                    //     ? theme.palette.error.lighter
                    //     : row.status === 'Pending'
                    //     ? theme.palette.warning.lighter
                    //     : theme.palette.secondary.lighter,
                  }}
                >
                  <Stack px={2} pt={2}>
                    <Stack direction="row" alignItems="start" justifyContent="space-between" gap={2} pl={0}>
                      <Stack direction="row" alignItems="center" gap={2} pl={0}>
                        <Stack
                          sx={{
                            // borderBottom: 2,
                            // borderRight: 2,
                            border: 2,
                            borderColor:
                              row.status === 'Payment Initiated'
                                ? theme.palette.warning.lighter
                                : row.status === 'Paid'
                                ? theme.palette.success.lighter
                                : row.status === 'Failed'
                                ? theme.palette.error.lighter
                                : row.status === 'Cancelled'
                                ? theme.palette.error.lighter
                                : row.status === 'Pending'
                                ? theme.palette.warning.lighter
                                : theme.palette.secondary.lighter,
                            // position: 'absolute',
                            // top: -4,
                            // left: -4,
                            borderRadius: 50,
                          }}
                        >
                          {matchedStudents.length > 0 ? (
                            matchedStudents.map((student, idx) => (
                              <Avatar
                                key={student.studentId}
                                alt=""
                                src={
                                  student.studentGender === 0 ? man : student.studentGender === 1 ? woman : undefined
                                }
                                sx={{
                                  border: 1,
                                  borderColor: 'white',
                                  width: 40,
                                  height: 40,
                                  ml: idx === 0 ? 0 : -1, // overlap avatars
                                  zIndex: matchedStudents.length - idx,
                                  bgcolor: 'grey.100',
                                }}
                              />
                            ))
                          ) : (
                            <Avatar sx={{ width: 40, height: 40 }} />
                          )}
                        </Stack>
                        <Stack>
                          <Typography variant="subtitle2" fontFamily="Poppins Semibold" fontSize={13}>
                            {row.studentName}
                          </Typography>
                          <Stack direction="row" alignItems="center" gap={1}>
                            {/* <Typography variant="body2" fontSize={12} color="text.secondary">
                              Class&nbsp;:
                            </Typography> */}
                            <Typography variant="subtitle2" fontSize={11}>
                              {row.className}
                            </Typography>
                          </Stack>
                        </Stack>
                      </Stack>
                      <Stack direction="row" alignItems="center" gap={2} pl={0}>
                        <Chip
                          size="small"
                          icon={config.icon}
                          label={row.status}
                          variant="filled"
                          color={config.color}
                          sx={{
                            ml: 'auto',
                            fontFamily: 'Poppins Semibold',
                            fontWeight: 600,
                            fontSize: 12,
                          }}
                        />
                      </Stack>
                    </Stack>
                    <Stack direction="row" columnGap={2} alignItems="center" pt={1} flexWrap="wrap">
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography width={60} variant="body2" fontSize={12} color="text.secondary">
                          Amount
                        </Typography>
                        <Typography variant="body2" fontSize={12} color="text.secondary">
                          :
                        </Typography>
                        <Typography variant="subtitle2" fontSize={12} color={theme.palette.success.main}>
                          {Number(row.grandTotal).toLocaleString('en-IN')}
                        </Typography>
                      </Stack>
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography width={60} variant="body2" fontSize={12} color="text.secondary">
                          Paid Date
                        </Typography>
                        <Typography variant="body2" fontSize={12} color="text.secondary">
                          :
                        </Typography>
                        <Typography variant="subtitle2" fontSize={12}>
                          {dayjs(row.paidDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, h:mm a')}
                        </Typography>
                      </Stack>
                    </Stack>
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Typography width={60} variant="caption" fontSize={12} color="text.secondary">
                        Order No
                      </Typography>
                      <Typography variant="body2" fontSize={12} color="text.secondary">
                        :
                      </Typography>
                      <Typography variant="subtitle2" fontSize={12} mt={!row.billdeskOrderNumber ? 0.5 : 0}>
                        {row.billdeskOrderNumber || '--'}
                      </Typography>

                      {row.billdeskOrderNumber && (
                        <IconButton
                          size="small"
                          onClick={() => handleCopy(row.billdeskOrderNumber, 'Order Number')}
                          sx={{ p: 0.5 }}
                        >
                          <ContentCopyIcon sx={{ fontSize: 12 }} />
                        </IconButton>
                      )}
                    </Stack>
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Typography width={60} variant="caption" fontSize={12} color="text.secondary">
                        Txn Id
                      </Typography>
                      <Typography variant="body2" fontSize={12} color="text.secondary">
                        :
                      </Typography>
                      <Typography variant="subtitle2" fontSize={12} mt={!row.billdeskTransactionId ? 0.5 : 0}>
                        {row.billdeskTransactionId || '--'}
                      </Typography>

                      {row.billdeskTransactionId && (
                        <IconButton
                          size="small"
                          onClick={() => handleCopy(row.billdeskTransactionId, 'Transaction Id')}
                          sx={{ p: 0.5 }}
                        >
                          <ContentCopyIcon sx={{ fontSize: 12 }} />
                        </IconButton>
                      )}
                    </Stack>
                  </Stack>
                  <Divider sx={{ borderColor: theme.palette.grey[400], borderBottomWidth: 1 }} />
                  <Stack direction="row" gap={1} mt={1} px={2}>
                    {row.status === 'Paid' && (
                      <Button
                        fullWidth
                        // disabled={row.status === 'Cancelled'}
                        size="small"
                        color="error"
                        startIcon={<BlockIcon />}
                        variant="outlined"
                        onClick={() => onUnpay(row)}
                        sx={{ fontSize: 12 }}
                      >
                        Unpay
                      </Button>
                    )}
                    {row.status === 'Cancelled' && (
                      <Button
                        fullWidth
                        // disabled={row.status === 'Paid'}
                        size="small"
                        color="primary"
                        startIcon={<SuccessIcon />}
                        variant="outlined"
                        onClick={() => onApprove(row)}
                        sx={{ fontSize: 12 }}
                      >
                        Approve
                      </Button>
                    )}
                    <Button
                      fullWidth
                      disabled={row.status === 'Cancelled'}
                      size="small"
                      color="success"
                      startIcon={<PrintIcon />}
                      variant="outlined"
                      onClick={() => onPrint(row)}
                      sx={{ fontSize: 12 }}
                    >
                      Print
                    </Button>

                    <Button
                      fullWidth
                      disabled={row.status === 'Cancelled'}
                      size="small"
                      color="info"
                      startIcon={<PictureAsPdfRoundedIcon />}
                      variant="outlined"
                      onClick={() => onPdf(row)}
                      sx={{ fontSize: 12 }}
                    >
                      PDF
                    </Button>
                  </Stack>
                </Paper>
              </Grid>
            );
          })}
        </Grid>
      )}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={2000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <MuiAlert severity="success" sx={{ width: '100%' }}>
          {snackbarMsg}
        </MuiAlert>
      </Snackbar>
    </>
  );
}
