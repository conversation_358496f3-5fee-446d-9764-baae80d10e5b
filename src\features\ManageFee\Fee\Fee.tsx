/* eslint-disable no-nested-ternary */
import { Box, Grid, MenuItem, Skeleton, SelectChangeEvent, Stack, Typography, Card, Select } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewStatusListData, getFeeOverviewStatusListStatus } from '@/config/storeSelectors';
import { ClassListInfo } from '@/types/AcademicManagement';
import React, { useEffect, useState } from 'react';
import { fetchFeeOverviewStatus } from '@/store/ManageFee/manageFee.thunks';
import useAuth from '@/hooks/useAuth';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useSettings from '@/hooks/useSettings';
import { OverViewProps } from '@/types/Common';

const FeeRoot = styled.div``;

const ValueWithTrend = ({ value, prevValue, loading }: { value: number; prevValue?: number; loading?: boolean }) => {
  if (loading) {
    return <Skeleton variant="rounded" width={170} height={25} />;
  }

  const increased = prevValue !== undefined && value > prevValue;
  const decreased = prevValue !== undefined && value < prevValue;

  return (
    <>
      <Typography variant="subtitle2" fontFamily="Poppins Semibold" fontSize={16}>
        {Number(value ?? 0).toLocaleString('en-IN')}
      </Typography>
      {prevValue !== undefined && (
        <Box
          bgcolor={increased ? '#bbf7d0' : decreased ? '#fecaca' : '#e5e7eb'}
          px={1.5}
          ml={1}
          color={increased ? 'green' : decreased ? 'red' : 'gray'}
          sx={{ display: 'inline-flex', borderRadius: '20px' }}
        >
          {increased && <TrendingUpIcon fontSize="small" />}
          {decreased && <TrendingDownIcon fontSize="small" />}
        </Box>
      )}
    </>
  );
};

export const Fee = ({ academicId, feeTypeId }: OverViewProps) => {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const feeOverviewStatusListData = useAppSelector(getFeeOverviewStatusListData);
  const feeOverviewStatusListStatus = useAppSelector(getFeeOverviewStatusListStatus);
  const ClassData = useAppSelector(getClassData);

  const AllClassOption: ClassListInfo = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState<ClassListInfo>(classDataWithAllClass[0]);

  const { classId, className } = classOptions || {};
  const {
    prevoiusMonthCollection,
    thisMonthCollection,
    thisYearTotalBalance,
    thisYearTotalFee,
    thisYearTotalPaid,
    todaysCollection,
  } = feeOverviewStatusListData || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewStatus({ adminId, academicId, classId, feeTypeId }));
  }, [dispatch, adminId, classId, academicId, feeTypeId]);

  const loading = feeOverviewStatusListStatus === 'loading';

  return (
    <FeeRoot>
      <Card
        sx={{
          boxShadow: 0,
          height: { xs: '100%', lg: '235px' },
          backgroundColor: isLight
            ? '#d2f3dd'
            : // theme.palette.success.main
              theme.palette.grey[900],
        }}
      >
        {/* Decorative Smooth Wave */}
        {/* Decorative Circles */}
        <svg
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none', // avoid blocking clicks
          }}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1400 320"
          preserveAspectRatio="xMidYMid meet" // keep aspect ratio
        >
          {/* Top-left circle */}
          <circle cx="70" cy="310" r="240" fill={theme.palette.success.main} fillOpacity="0.10">
            <animate
              attributeName="r"
              values="220;250;220" // grow to 220px, shrink back
              dur="5s" // duration of full cycle
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="70" cy="310" r="160" fill={theme.palette.success.main} fillOpacity="0.10" />

          {/* Bottom-right circle */}
          <circle cx="1250" cy="50" r="250" fill={theme.palette.success.main} fillOpacity="0.10">
            <animate
              attributeName="r"
              values="230;250;230" // grow to 220px, shrink back
              dur="5s" // duration of full cycle
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="1250" cy="50" r="200" fill={theme.palette.success.main} fillOpacity="0.10" />
        </svg>

        <Box display="flex" p={2} alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontSize={14}>
            Fee Status
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }}>
            <Select
              size="small"
              sx={{
                // backgroundColor: theme.palette.common.white,
                // color: theme.palette.primary.main,
                height: 30,
              }}
              value={className}
              onChange={handleChange}
              displayEmpty
              MenuProps={{
                PaperProps: {
                  style: { maxHeight: '250px' },
                },
              }}
            >
              {classDataWithAllClass.map((item) => (
                <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Box>

        <Grid container spacing={{ xs: 1.5, sm: 2 }} sx={{ px: 2, pb: 2 }}>
          {[
            {
              id: 1,
              label: 'This Year Total Fee',
              value: thisYearTotalFee ?? 0,
              prev: prevoiusMonthCollection,
            },
            {
              id: 2,
              label: 'This Year Total Paid',
              value: thisYearTotalPaid ?? 0,
              prev: prevoiusMonthCollection,
            },
            {
              id: 3,
              label: 'This Year Total Balance',
              value: thisYearTotalBalance ?? 0,
              prev: thisYearTotalPaid,
            },
            {
              id: 4,
              label: 'Previous Month Collection',
              value: prevoiusMonthCollection ?? 0,
            },
            {
              id: 5,
              label: 'This Month Collection',
              value: thisMonthCollection ?? 0,
            },
            {
              id: 6,
              label: 'Todays Collection',
              value: todaysCollection ?? 0,
            },
          ].map((item) => (
            <Grid item lg={4} md={6} sm={6} xs={6} key={item.id}>
              <Card sx={{ p: 2, boxShadow: 0 }}>
                <Typography
                  variant="subtitle1"
                  fontFamily="Poppins Semibold"
                  fontSize={{ xs: 10, sm: 12 }}
                  pb={1}
                  color="textSecondary"
                >
                  {item.label}
                </Typography>
                <Stack direction="row" alignItems="center">
                  <CurrencyRupeeIcon sx={{ color: theme.palette.success.main, fontSize: { xs: '16px', sm: '16px' } }} />
                  <ValueWithTrend value={item.value} prevValue={item.prev} loading={loading} />
                </Stack>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Card>
    </FeeRoot>
  );
};
