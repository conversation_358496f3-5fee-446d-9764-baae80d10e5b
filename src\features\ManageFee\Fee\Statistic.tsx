import BarChart from '@/components/Dashboard/BarChart';
import Typography from '@mui/material/Typography';
import { Box, Card, Stack } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewChartListData } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import NodataGraphIcon from '@/assets/Graph.png';
import { fetchFeeOverviewChart } from '@/store/ManageFee/manageFee.thunks';
import useSettings from '@/hooks/useSettings';
import { OverViewProps } from '@/types/Common';
import StatisticsIcon from '@/assets/ManageFee/Statistics.json';
import Lottie from 'lottie-react';

const StatisticRoot = styled.div`
  width: 100%;
  .apexcharts-menu-icon {
    display: none;
  }
`;

function Statistic({ academicId, feeTypeId }: OverViewProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  // const dashboardFeeChartStatus = useAppSelector(getdashboardFeeChartStatus);
  const feeOverviewChartListData = useAppSelector(getFeeOverviewChartListData);
  // const ClassStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };
  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewChart({ adminId, academicId, classId, feeTypeId }));
  }, [dispatch, adminId, classId, academicId, feeTypeId]);

  const totalFeeSum = feeOverviewChartListData.reduce((acc, currentMonth) => {
    return acc + parseFloat(currentMonth.totalFeeCollected);
  }, 0);
  console.log(totalFeeSum);
  const barChartDataFeeStatistic = [
    { name: 'Fee', data: feeOverviewChartListData.map((item) => item.totalFeeCollected) },
  ];

  const barChartOptionFeeStatistic = {
    chart: {
      foreColor: '#732ebe',
    },
    colors: ['#732ebe'],
    // stroke: ['100px'],
    noData: { style: {} },
    xaxis: {
      categories: feeOverviewChartListData.map((item) => item.monthName.substring(0, 3)),
      labels: {
        style: {
          fontWeight: 600,
          fontSize: '10',
        },
      },
      axisBorder: {
        show: true,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        style: {
          fontWeight: 600,
        },
      },
    },
    grid: {
      borderColor: 'rgba(163, 174, 208, 0.3)',
      show: true,
      yaxis: {
        lines: {
          show: false,
        },
      },
      row: {
        opacity: 0.5,
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      type: 'gradient',
    },
    plotOptions: {
      bar: {
        borderRadius: 10,
        borderRadiusApplication: 0,
        columnWidth: '50%',
      },
    },
    tooltip: {
      style: {},
      theme: 'dark',
    },

    dataLabels: {
      enabled: false,
      style: {
        colors: ['#fff'],
        // fontSize: '8',
      },
    },
  };

  return (
    <StatisticRoot>
      <Card
        sx={{
          boxShadow: 0,
          height: '235px',
          backgroundColor: isLight ? theme.palette.chart.violet[5] : theme.palette.grey[900],
        }}
      >
        <svg
          style={{
            position: 'absolute',
            top: -75,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            // rotate: '180deg',
            zIndex: -1,
          }}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Static wave path */}
          <path
            fill={theme.palette.chart.violet[4]}
            fillOpacity="0.1"
            d="M0,32L24,42.7C48,53,96,75,144,85.3C192,96,240,96,288,117.3C336,139,384,181,432,181.3C480,181,528,139,576,106.7C624,75,672,53,720,53.3C768,53,816,75,864,90.7C912,107,960,117,1008,144C1056,171,1104,213,1152,197.3C1200,181,1248,107,1296,96C1344,85,1392,139,1416,165.3L1440,192L1440,0L1416,0C1392,0,1344,0,1296,0C1248,0,1200,0,1152,0C1104,0,1056,0,1008,0C960,0,912,0,864,0C816,0,768,0,720,0C672,0,624,0,576,0C528,0,480,0,432,0C384,0,336,0,288,0C240,0,192,0,144,0C96,0,48,0,24,0L0,0Z"
          />
          <path fill={theme.palette.chart.violet[4]} fillOpacity="0.1">
            <animate
              attributeName="d"
              dur="8s"
              repeatCount="indefinite"
              values="
      M0,160L24,165.3C48,171,96,181,144,197.3C192,213,240,235,288,208C336,181,384,107,432,106.7C480,107,528,181,576,218.7C624,256,672,256,720,234.7C768,213,816,171,864,144C912,117,960,107,1008,101.3C1056,96,1104,96,1152,128C1200,160,1248,224,1296,245.3C1344,267,1392,245,1416,234.7L1440,224L1440,0L0,0Z;
      M0,150L24,160C48,170,96,180,144,200C192,220,240,240,288,210C336,180,384,120,432,115C480,110,528,180,576,210C624,240,672,240,720,220C768,200,816,150,864,130C912,110,960,100,1008,105C1056,110,1104,110,1152,140C1200,170,1248,220,1296,240C1344,260,1392,240,1416,230L1440,220L1440,0L0,0Z;
      M0,160L24,165.3C48,171,96,181,144,197.3C192,213,240,235,288,208C336,181,384,107,432,106.7C480,107,528,181,576,218.7C624,256,672,256,720,234.7C768,213,816,171,864,144C912,117,960,107,1008,101.3C1056,96,1104,96,1152,128C1200,160,1248,224,1296,245.3C1344,267,1392,245,1416,234.7L1440,224L1440,0L0,0Z
    "
            />
          </path>

          {/* <path fill={theme.palette.chart.violet[4]} fillOpacity="0.1">
            <animate
              attributeName="d"
              dur="10s"
              repeatCount="indefinite"
              values="
       M0,160L24,165.3C48,171,96,181,144,197.3C192,213,240,235,288,208C336,181,384,107,432,106.7C480,107,528,181,576,218.7C624,256,672,256,720,234.7C768,213,816,171,864,144C912,117,960,107,1008,101.3C1056,96,1104,96,1152,128C1200,160,1248,224,1296,245.3C1344,267,1392,245,1416,234.7L1440,224L1440,0L1416,0C1392,0,1344,0,1296,0C1248,0,1200,0,1152,0C1104,0,1056,0,1008,0C960,0,912,0,864,0C816,0,768,0,720,0C672,0,624,0,576,0C528,0,480,0,432,0C384,0,336,0,288,0C240,0,192,0,144,0C96,0,48,0,24,0L0,0Z
      "
            />
          </path> */}
          {/* <path
            fill={theme.palette.chart.violet[4]}
            fillOpacity="0.1"
            d="M0,32L24,32C48,32,96,32,144,69.3C192,107,240,181,288,197.3C336,213,384,171,432,149.3C480,128,528,128,576,122.7C624,117,672,107,720,122.7C768,139,816,181,864,176C912,171,960,117,1008,128C1056,139,1104,213,1152,256C1200,299,1248,309,1296,282.7C1344,256,1392,192,1416,160L1440,128L1440,0L1416,0C1392,0,1344,0,1296,0C1248,0,1200,0,1152,0C1104,0,1056,0,1008,0C960,0,912,0,864,0C816,0,768,0,720,0C672,0,624,0,576,0C528,0,480,0,432,0C384,0,336,0,288,0C240,0,192,0,144,0C96,0,48,0,24,0L0,0Z"
          />{' '} */}
          {/* <path
            fill={theme.palette.chart.violet[4]}
            fillOpacity="0.1"
            d=" M0,192L48,160C96,128,192,64,288,80C384,96,480,192,576,192C672,192,768,96,864,90.7C960,85,1056,171,1152,208C1248,245,1344,235,1392,229.3L1440,224L1440,0L0,0Z;
        M0,160L48,180C96,200,192,240,288,220C384,200,480,120,576,130C672,140,768,210,864,200C960,190,1056,130,1152,120C1248,110,1344,180,1392,200L1440,220L1440,0L0,0Z;
        M0,192L48,160C96,128,192,64,288,80C384,96,480,192,576,192C672,192,768,96,864,90.7C960,85,1056,171,1152,208C1248,245,1344,235,1392,229.3L1440,224L1440,0L0,0Z"
          /> */}
          {/* Animated wave path */}
          {/* <path fill={theme.palette.chart.violet[4]} fillOpacity="0.15">
            <animate
              attributeName="d"
              dur="10s"
              repeatCount="indefinite"
              values="
        M0,192L48,160C96,128,192,64,288,80C384,96,480,192,576,192C672,192,768,96,864,90.7C960,85,1056,171,1152,208C1248,245,1344,235,1392,229.3L1440,224L1440,0L0,0Z;
        M0,160L48,180C96,200,192,240,288,220C384,200,480,120,576,130C672,140,768,210,864,200C960,190,1056,130,1152,120C1248,110,1344,180,1392,200L1440,220L1440,0L0,0Z;
        M0,192L48,160C96,128,192,64,288,80C384,96,480,192,576,192C672,192,768,96,864,90.7C960,85,1056,171,1152,208C1248,245,1344,235,1392,229.3L1440,224L1440,0L0,0Z
      "
            />
          </path> */}
        </svg>

        <Box display="flex" p={2} alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontSize={14}>
            Fee Statistic
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }}>
            {/* <SelectBox Selection_Options={YEAR_SELECT} placeholder="Year" /> */}
            <Select
              sx={{
                // backgroundColor: theme.palette.common.white, color: theme.palette.primary.main,
                height: 30,
              }}
              value={className}
              onChange={handleChange}
              displayEmpty
              labelId="demo-dialog-select-label"
              id="demo-dialog-select"
              inputProps={{ 'aria-label': 'Without label' }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: '250px', // Adjust the value to your desired height
                  },
                },
              }}
            >
              {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
              {classDataWithAllClass?.map((item: ClassListInfo) => (
                <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Box>
        <Box sx={{ overflowX: 'scroll', height: '100%' }}>
          {totalFeeSum === 0 ? (
            <Stack
              // sx={{ height: { sm: '140px' } }}
              direction="column"
              justifyContent="center"
              alignItems="center"
              spacing={1}
              my={2}
              overflow="hidden"
            >
              {/* <Lottie animationData={StatisticsIcon} loop style={{ width: '350px', height: '200px' }} /> */}
              <img width={140} src={NodataGraphIcon} alt="" style={{ opacity: 0.8 }} />
              <Typography variant="subtitle2" color="textSecondary">
                No Fees Data in this Class.
              </Typography>
            </Stack>
          ) : (
            <Box sx={{ width: { sm: '100%', xs: '550px' }, height: '170px' }}>
              <BarChart chartDatas={barChartDataFeeStatistic} chartOptions={barChartOptionFeeStatistic} height="100%" />
            </Box>
          )}
        </Box>
      </Card>
    </StatisticRoot>
  );
}

export default Statistic;
