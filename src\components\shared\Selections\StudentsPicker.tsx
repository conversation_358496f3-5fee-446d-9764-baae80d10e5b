import * as React from 'react';
import Chip from '@mui/material/Chip';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { getClassData, getParentsListData, getStudentPickerData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchParentsList } from '@/store/MessageBox/messageBox.thunks';
import SearchIcon from '@mui/icons-material/Search';
import { Box, useTheme, Avatar, Typography, IconButton } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import styled from 'styled-components';
import { fetchStudentPickerData } from '@/store/ManageFee/manageFee.thunks';
import { GetStudentPickerDataType, StudentPickerRequest } from '@/types/ManageFee';
import { FiSearch } from 'react-icons/fi';

const StudentsPickerRoot = styled.div`
  .MuiAutocomplete-tag {
    margin: 1px;
  }
`;
type StudentsPickerFieldProps = {
  setSelectedStudentIds: React.Dispatch<React.SetStateAction<string>>;
  classId: number;
  academicId: number;
  multiple?: boolean | undefined;
  loadRecentPaidList?: any;
  currentRecentPaidListRequest?: any;
  width?: number | string;
  componentsPropsWidth?: number | string;
  setSelectedStudentData?: React.Dispatch<React.SetStateAction<any[]>>;
  variant?: 'standard' | 'filled' | 'outlined' | undefined;
};
export default function StudentsPickerField({
  loadRecentPaidList,
  currentRecentPaidListRequest,
  setSelectedStudentIds,
  setSelectedStudentData,
  classId,
  academicId,
  multiple,
  width,
  componentsPropsWidth,
  variant,
}: StudentsPickerFieldProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const ClassData = useAppSelector(getClassData);
  const [selectedData, setSelectedData] = React.useState<any[]>([]);
  const ParentsListData = useAppSelector(getParentsListData);
  const StudentPickerData = useAppSelector(getStudentPickerData);
  const [studentSearch, setStudentSearch] = React.useState('');

  const currentParentListRequest: StudentPickerRequest = React.useMemo(
    () => ({
      adminId,
      academicId,
      classId,
      search: studentSearch,
    }),
    [adminId, academicId, classId, studentSearch]
  );

  React.useEffect(() => {
    if (classId !== -1 || studentSearch?.length >= 3) {
      dispatch(fetchStudentPickerData(currentParentListRequest));
    }
  }, [dispatch, currentParentListRequest, classId, studentSearch]);

  return (
    <StudentsPickerRoot>
      <Stack spacing={3}>
        <Autocomplete
          variant
          multiple={multiple}
          id="tags-outlined"
          sx={{
            borderRadius: 1,
            width: width ?? { xs: 250, sm: 350, lg: 350 },
            '& .MuiInputBase-input': {
              padding: '0.57rem 0.675rem',
              // height: 2,
            },
          }}
          limitTags={1}
          // getLimitTagsText={1}
          options={StudentPickerData}
          // disableClearable={false}
          // clearIcon
          // clearIcon={!multiple}
          // disableClearable={multiple}
          // disableCloseOnSelect
          forcePopupIcon={false}
          openOnFocus={false}
          filterOptions={(options) => options} // Disables filtering
          disablePortal
          getOptionLabel={(option) => option.studentName || ''}
          componentsProps={{
            paper: {
              sx: {
                width: componentsPropsWidth ?? '100%',
              },
            },
          }}
          // onChange={(e, newValue) => {
          //   // Extract studentId values and format them as a comma-separated string
          //   const studentIds = newValue.map((student) => student.studentId).join(',');
          //   setSelectedStudentIds(studentIds);
          //   setSelectedStudentData?.(newValue);
          //   // Pass the formatted string to currentRecentPaidListRequest
          //   loadRecentPaidList({ ...currentRecentPaidListRequest, studentId: studentIds });
          // }}
          onChange={(e, newValue) => {
            if (multiple) {
              const studentIds = newValue.map((student) => student.studentId).join(',');
              setSelectedStudentIds(studentIds);
              setSelectedStudentData?.(newValue);
              loadRecentPaidList?.({ ...currentRecentPaidListRequest, studentId: studentIds });
            } else {
              const studentId = newValue?.studentId || '';
              setSelectedStudentIds(studentId);
              setSelectedStudentData?.(newValue ? [newValue] : []);
              loadRecentPaidList?.({ ...currentRecentPaidListRequest, studentId });
            }
          }}
          // renderTags={(value, getTagProps) =>
          //   value.map((option, index) => <Chip size="small" label={option.studentName} {...getTagProps({ index })} />)
          // }
          renderTags={
            multiple
              ? (value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      size="small"
                      label={option.studentName}
                      {...getTagProps({ index })}
                      sx={{
                        height: 18, // shorter chip
                        fontSize: '11px', // smaller text
                        paddingX: 0.1, // less horizontal space
                        '& .MuiChip-label': {
                          // padding: 0, // remove extra padding inside
                        },
                      }}
                    />
                  ))
              : undefined
          }
          renderOption={(props, option) => (
            <div style={{ borderBottom: `1px solid ${theme.palette.grey[200]}` }}>
              <Box component="li" sx={{}} {...props} display="flex" gap={1} alignItems="center">
                <Avatar sx={{ width: '50px', height: '50px' }} alt="" src={option.image} />
                <Box display="flex" width="100%" justifyContent="space-between">
                  <Stack direction="column" gap={0.5}>
                    <Typography width={180} variant="subtitle2" fontSize="11px" fontWeight="bold">
                      {option.studentName}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.grey[600]}>
                      Class : {option.className}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.grey[600]}>
                      Gender : {option.gender}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.warning.main}>
                      Father : {option.fatherName}
                    </Typography>
                    <Typography width={180} variant="subtitle2" fontSize="11px" color={theme.palette.warning.main}>
                      Mother : {option.motherName}
                    </Typography>
                  </Stack>
                  <Stack direction="column" justifyContent="space-between" alignItems="flex-end">
                    <Chip
                      variant="filled"
                      className=""
                      color="success"
                      sx={{ height: 20, fontWeight: 500 }}
                      label="Active"
                    />

                    <Typography variant="subtitle2" fontSize="11px" color={theme.palette.primary.main}>
                      AD : {option.admissionNo}
                    </Typography>
                    <Typography visibility="hidden" variant="subtitle2" fontSize="11px" color={theme.palette.info.main}>
                      {option.studentId}
                    </Typography>
                    <Typography variant="subtitle2" fontSize="11px" color={theme.palette.info.main}>
                      {option.fatherNumber}
                    </Typography>
                    <Typography variant="subtitle2" fontSize="11px" color={theme.palette.info.main}>
                      {option.motherNumber}
                    </Typography>
                  </Stack>
                </Box>
              </Box>
            </div>
          )}
          filterSelectedOptions
          renderInput={(params) => (
            <TextField
              variant={variant}
              {...params}
              InputProps={{
                ...params.InputProps,
                style: {
                  //  borderRadius: 50,
                  height: variant === 'standard' ? 38.5 : '',
                  // paddingLeft: 30,
                },
                startAdornment: (
                  <>
                    <Stack
                    //  sx={{ position: 'absolute', left: 8 }}
                    >
                      <FiSearch color={theme.palette.grey[600]} />
                      {/* <SearchIcon fontSize="small" color="secondary" /> */}
                    </Stack>
                    {params.InputProps.startAdornment}
                  </>
                ),
                endAdornment: params.InputProps.endAdornment,
                disableUnderline: true,
              }}
              placeholder="Search student"
              onChange={(e) => {
                const { value } = e.target;
                if (value.length >= 3) {
                  setStudentSearch(value);
                } else {
                  setStudentSearch(''); // Clear the search state if input is less than 3 characters
                }
              }}
            />
          )}
        />
      </Stack>
    </StudentsPickerRoot>
  );
}
