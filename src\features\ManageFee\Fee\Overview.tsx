/* eslint-disable import/no-cycle */
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import {
  Card,
  Grid,
  MenuItem,
  SelectChangeEvent,
  Typography,
  Select,
  Stack,
  Button,
  ToggleButton,
  IconButton,
  ToggleButtonGroup,
  useMediaQuery,
  useTheme,
  Box,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import { getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { Link } from 'react-router-dom';
import TableRowsIcon from '@mui/icons-material/TableRows';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import PaymentIcon from '@mui/icons-material/Payment';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import Statistic from './Statistic';
import { Fee } from './Fee';
import { ReceiveAmount } from './ReceiveAmount';
import { RecentPaidList } from './RecentPaidList';

const OverviewRoot = styled.div`
  padding: 1rem;
  @media (max-width: 576px) {
    padding: 0rem;
    .overview-card {
      border-radius: 0;
    }
  }
  /* height: calc(100vh - 160px); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
`;

const Overview = ({ handleShowPayFee }: any) => {
  const theme = useTheme();
  const YearData = useAppSelector(getYearData);
  const defaultYear = YearData[0]?.accademicId || 0;
  const defualtFeeType = FEE_TYPE_ID_OPTIONS[0]?.id || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState<string>(defaultYear.toString());
  // YearData && YearData.length > 0 ? YearData[0].accademicId : 0
  const [feeTypeFilter, setFeeTypeFilter] = useState(defualtFeeType);
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const [isCardView, setIsCardView] = React.useState(false);
  const adminId: number = user ? user.accountId : 0;
  const [switchPaidAmountList, setSwitchPaidAmountList] = React.useState<'recentPaidList' | 'paymentModeList'>(
    'recentPaidList'
  );

  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));

  // Automatically switch view based on screen size
  useEffect(() => {
    if (isSmallScreen) {
      setIsCardView(true); // Enable Card view on small screen
    } else if (isLargeScreen) {
      setIsCardView(false); // Force Table view on large screens
    }
    // Do nothing on medium screens — keeps current value
  }, [isSmallScreen, isLargeScreen]);

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    // console.log('YearData::::----', YearData);
  }, [dispatch, adminId]);

  const handleChange = (_event: React.MouseEvent<HTMLElement>, newAlignment: 'recentPaidList' | 'paymentModeList') => {
    if (newAlignment !== null) {
      setSwitchPaidAmountList(newAlignment);
    }
  };

  // const handleYearChange = (e: SelectChangeEvent) => {
  //   setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
  //   // loadFeePaidList({ ...currentFeePaidListRequest, academicId: parseInt(e.target.value, 10) });
  // };

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedId = e.target.value; // string
    setAcademicYearFilter(selectedId);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    // loadFeePaidList({ ...currentFeePaidListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };
  return (
    <Page title="Fee">
      <OverviewRoot>
        <Card className="overview-card" sx={{ px: { xs: 1.5, md: 4 }, py: { xs: 2, md: 2 } }}>
          <Box
            position="relative"
            display="flex"
            // flexDirection={{ xs: 'column', lg: 'row' }}
            alignItems={{ xs: 'start', lg: 'center' }}
            justifyContent="space-between"
            pb={{ xs: 3, md: 2 }}
          >
            <Stack flex={1} direction={{ xs: 'column', sm: 'column', md: 'column', lg: 'row' }} gap={3} flexWrap="wrap">
              <Typography variant="h6" fontSize={17}>
                Fee Overview
              </Typography>
              <Stack direction={{ xs: 'row', sm: 'row' }} gap={2} flex={1}>
                {/* Academic Year Select */}
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  sx={{
                    height: 33,
                    flex: { xs: '1 1 100%', lg: '0 0 auto' },
                    minWidth: { lg: 120 },
                  }}
                >
                  <MenuItem value={0} sx={{ display: 'none' }}>
                    Select Year
                  </MenuItem>
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>

                {/* Fee Type Select */}
                <Select
                  labelId="feeTypeFilter"
                  id="feeTypeFilterSelect"
                  value={feeTypeFilter.toString()}
                  onChange={handleFeeTypeChange}
                  sx={{
                    height: 33,
                    flex: { xs: '1 1 100%', lg: '0 0 auto' },
                    minWidth: { lg: 150 },
                  }}
                >
                  <MenuItem value={0} sx={{ display: 'none' }}>
                    Select Type
                  </MenuItem>
                  {FEE_TYPE_ID_OPTIONS.map((opt) => (
                    <MenuItem key={opt.id} value={opt.id}>
                      {opt.name}
                    </MenuItem>
                  ))}
                </Select>
              </Stack>
            </Stack>
            <Box
              position={{ xs: 'absolute', lg: 'relative' }}
              right={{ xs: 0, lg: 'auto' }}
              display="flex"
              justifyContent={{ xs: 'flex-start', sm: 'flex-end' }}
            >
              <Link to="/manage-fee/pay-fee-details">
                <Button
                  size="small"
                  color="success"
                  variant="contained"
                  // sx={{ py: 0.7, px: 3 }}
                  onClick={handleShowPayFee}
                  startIcon={<PaymentIcon />} // icon at the start of button
                >
                  Pay Fee
                </Button>
              </Link>
            </Box>
          </Box>

          <Grid container spacing={3} pb={2}>
            <Grid item xl={7} lg={12} xs={12}>
              <Fee academicId={Number(academicYearFilter)} feeTypeId={feeTypeFilter} />
            </Grid>
            <Grid item xl={5} lg={12} xs={12}>
              <Statistic academicId={Number(academicYearFilter)} feeTypeId={feeTypeFilter} />
            </Grid>
          </Grid>
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
            <ToggleButtonGroup
              color="primary"
              value={switchPaidAmountList}
              exclusive
              onChange={handleChange}
              aria-label="Platform"
              size="small"
              sx={{
                backgroundColor: theme.palette.grey[200],
                border: `1px solid ${theme.palette.common.white}`,

                '& .MuiToggleButton-root': {
                  py: 1,
                  fontFamily: 'Poppins Semibold',
                  borderRadius: '20px',
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.common.white,
                    color: theme.palette.primary.main,
                    '&:hover': {
                      backgroundColor: theme.palette.common.white,
                      color: theme.palette.primary.main,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.grey[200],
                    color: theme.palette.primary.main,
                  },
                },
              }}
            >
              <ToggleButton sx={{ fontWeight: 'bold' }} value="recentPaidList">
                Recent Paid List
              </ToggleButton>
              <ToggleButton sx={{ fontWeight: 'bold' }} value="paymentModeList">
                Payment Mode List
              </ToggleButton>
            </ToggleButtonGroup>

            {switchPaidAmountList === 'recentPaidList' && (
              <IconButton
                onClick={() => setIsCardView((prev) => !prev)}
                sx={{ ml: 1 }}
                title={isCardView ? 'Show Table View' : 'Show Card View'}
              >
                {isCardView ? <TableRowsIcon /> : <ViewModuleIcon />}
              </IconButton>
            )}
          </Stack>
          <Grid container spacing={3}>
            {switchPaidAmountList === 'recentPaidList' ? (
              <Grid item xl={12} lg={12} xs={12}>
                <RecentPaidList
                  isCardView={isCardView}
                  academicId={Number(academicYearFilter)}
                  feeTypeId={feeTypeFilter}
                />
              </Grid>
            ) : (
              <Grid item xl={12} lg={12} xs={12}>
                <ReceiveAmount academicId={Number(academicYearFilter)} feeTypeId={feeTypeFilter} />
              </Grid>
            )}
            {/* <Grid item xl={7} lg={12} xs={12}>
              <PendingFee academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid>
            <Grid item xl={5} lg={12} xs={12}>
              <ReceiveAmount academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid> */}
          </Grid>
        </Card>
      </OverviewRoot>
    </Page>
  );
};

export default Overview;
