/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
  Radio,
  FormHelperText,
  CircularProgress,
  Toolbar,
} from '@mui/material';
import NoData from '@/assets/no-datas.png';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import studentSuccessIcon from '@/assets/ManageFee/studentSuccessJsonIcon.json';
import studentSelectedIcon from '@/assets/ManageFee/studentSelectedIcon.json';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getManageFeeclassListData,
  getOptionalFeeSettingListData,
  getOptionalFeeSettingListStatus,
  getStopMappingSettingsData,
  getStopMappingSettingsStatus,
  getYearData,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  createStopMapping,
  DeleteAllBusMappedStudent,
  DeleteAllOptionalFee,
  DeleteBusMapped,
  DeleteOptionalFee,
  fetchClassList,
  fetchClassSections,
  fetchGetOptionalFeeSettings,
  fetchStopMappingSettings,
  optionalFeeSettings,
} from '@/store/ManageFee/manageFee.thunks';
import {
  BasicFeeMappedDeleteAllDataType,
  CreateStopMappingType,
  DeleteAllBusMappedStudentType,
  GetOptionalFeeSettingsDataType,
  GetOptionalFeeStudentMapDataType,
  GetStopTermMappedType,
  OptionalFeeMappedDeleteAllType,
  StopMappingSettingsType,
  StopTermMapped,
  StudentsMappedType,
  TermListType,
} from '@/types/ManageFee';
import { CloseIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import CloseIcon2 from '@mui/icons-material/Close';
import HorizontalRuleIcon from '@mui/icons-material/HorizontalRule';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import { Autocomplete } from '@mui/material';
import { TextField } from '@mui/material';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import ReportIcon from '@mui/icons-material/Report';
import { Checkbox } from '@mui/material';
import { Backdrop } from '@mui/material';
import { InputAdornment } from '@mui/material';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';
import { ArrowDownward, ArrowUpward } from '@mui/icons-material';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';

const OptionalFeeSetting2Root = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    /* height: calc(100vh - 160px); */
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }

        .MuiTableCell-root {
          border-color: white;
          /* border: 1px solid ${(props) => props.theme.palette.grey[100]}; */
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(n + 7):not(:last-child) {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }

        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#3984a4' : props.theme.palette.grey[900]};
          color: white;
        }
        @media screen and (min-width: 992px) {
          .MuiTableCell-head:nth-child(1) {
            z-index: 11;
            position: sticky;
            left: 1px;
          }
          .MuiTableCell-head:nth-child(2) {
            z-index: 11;
            position: sticky;
            left: 37px;
          }
          .MuiTableCell-head:nth-child(3) {
            z-index: 11;
            position: sticky;
            left: 112.5px;
          }
          .MuiTableCell-head:nth-child(4) {
            z-index: 11;
            position: sticky;
            left: 317px;
          }
          .MuiTableCell-head:nth-child(5) {
            z-index: 11;
            position: sticky;
            left: 432px;
          }
          .MuiTableCell-head:nth-child(6) {
            z-index: 11;
            position: sticky;
            left: 572.5px;
          }
          .MuiTableCell-head:last-child {
            z-index: 11;
            position: sticky;
            right: 0px;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
            position: sticky;
            left: 0.5px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            /* width: 50px; */
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
            position: sticky;
            left: 37px;
            /* padding-left: 5px; */
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
            position: sticky;
            left: 112.5px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(4) {
            position: sticky;
            left: 317px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(5) {
            position: sticky;
            left: 432px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(6) {
            position: sticky;
            left: 572.5px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[900]};
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:last-child {
            position: sticky;
            right: 0px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#e3f2f5' : props.theme.palette.grey[800]};
            z-index: 1;
          }
        }
      }
      .MuiTableCell-root {
        padding: 0px;
        height: 100%;
      }
      .MuiTableCell-root.MuiTableCell-head {
        padding-left: 5px;
      }
      .MuiTableCell-root.MuiTableCell-head:first-child {
        padding-left: 0px;
      }

      .MuiTableCell-root:first-child {
        /* padding: 5px; */
      }
    }
    .pointTitle {
      display: -webkit-box;
      max-width: 250px;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
`;

interface ExtendGetOptionalFeeSettingsDataType extends GetOptionalFeeSettingsDataType {
  studentId?: number;
}

type PickupDropListType = {
  feeId: number;
  feeTitle: string;
};
type BusListType = {
  busId: number;
  busName: string;
};

export default function OptionalFeeSetting2() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const academicId = 10;
  const [showFilter, setShowFilter] = useState(true);

  const YearData = useAppSelector(getYearData);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  // const classListStatus = useAppSelector(getclassListStatus);

  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [busListFilter, setBusListFilter] = useState<{ [key: number]: number }>({});
  const [allPickupListFilter, setAllPickupListFilter] = useState<{ [key: string]: number }>({});
  const [allDropListFilter, setAllDropListFilter] = useState<{ [key: string]: number }>({});
  const [individualPickupListFilter, setIndividualPickupListFilter] = useState<{ [key: string]: number }>({});
  const [individualDropListFilter, setIndividualDropListFilter] = useState<{ [key: string]: number }>({});
  const [selectionBoxValue, setSelectionBoxValue] = useState<boolean>(true);

  const stopMappingSettingsData = useAppSelector(getStopMappingSettingsData);
  const stopMappingSettingsStatus = useAppSelector(getStopMappingSettingsStatus);

  const [stopMappingListData, setStopMappingListData] = useState<StopMappingSettingsType[]>([]);
  const [studentsData, setStudentsData] = useState<StudentsMappedType[]>([]);
  const [pickupDropListDatas, setPickupDropListDatas] = useState<PickupDropListType[]>([]);
  const [busListDatas, setBusListDatas] = useState<BusListType[]>([]);
  const [clickedCells, setClickedCells] = useState<{ rowIndex: number; columnIndex: number; value: string }[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ studentId: number; termId: number }[]>([]);
  const [saving, setSaving] = useState(true);
  const [savingAll, setSavingAll] = useState(false);
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [showError, setShowError] = useState<{ [key: string]: boolean }>({});
  const [showErrorPickUp, setShowErrorPickUp] = useState<{ [key: string]: boolean }>({});
  const [showErrorDrop, setShowErrorDrop] = useState<{ [key: string]: boolean }>({});
  const [errorSaveAll, setErrorSaveAll] = useState<boolean>(false);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState({});
  const [checkedRows, setCheckedRows] = useState<{ [key: string]: boolean }>({});
  const [rowId, setRowId] = useState({});
  const [saveAllButtonDisabled, setSaveAllButtonDisabled] = useState<boolean>(true);
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<StudentsMappedType[]>([]);
  const [selectedEditRows, setSelectedEditRows] = useState([]);
  const [errorCountdownMap, setErrorCountdownMap] = useState<{ [key: string]: number }>({});
  const [stopMappedLength, setStopMappedLength] = useState<boolean>();
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');

  const textRef = useRef(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    if (textRef.current) {
      setIsOverflowing(textRef.current.scrollWidth > textRef.current.clientWidth);
    }
  }, []);

  const initialOptionalFeeRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: 0,
      classId: 0,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );

  const currentOptionalFeeRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter, feeTypeFilter]
  );

  const loadOptionalFeeList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number; feeTypeId: number }) => {
      try {
        // setSelectedCells([]);
        const data: StopMappingSettingsType[] = await dispatch(fetchStopMappingSettings(request)).unwrap();
        console.log('data::::', data);
        if (data) {
          const studentsMap: StudentsMappedType[] = data.studentsMapped
            ? data.studentsMapped.map((item: StudentsMappedType) => ({ ...item }))
            : [];
          setStudentsData(studentsMap);
          const length = studentsMap.reduce((total, item) => total + (item.stopTermMapped?.length || 0), 0);
          setStopMappedLength(length);

          console.log('length::::', length);
          console.log('studentsMap::::', studentsMap);

          const pickupDropListData: PickupDropListType[] = data.pickupDropList
            ? data.pickupDropList.map((item: PickupDropListType) => ({ ...item }))
            : [];
          setPickupDropListDatas(pickupDropListData);

          const busListData: BusListType[] = data.busList ? data.busList.map((item: BusListType) => ({ ...item })) : [];
          setBusListDatas(busListData);
        }
      } catch (error) {
        console.error('Error loading Optional fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    // if (optionalFeeSettingListStatus === 'idle') {
    // loadOptionalFeeList(currentOptionalFeeRequest);
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    dispatch(fetchClassSections({ adminId, academicId }));
    // }
    setStopMappingListData(stopMappingSettingsData);
    // const length =stopMappingSettingsData
    console.log('stopMappingListData::::----', stopMappingListData);
    console.log('selectedCells::::----', selectedCells);
    console.log('rowId::::----', rowId);
    console.log('individualSaveButtonEnabled::::----', individualSaveButtonEnabled);
  }, [
    dispatch,
    adminId,
    classSectionsFilter,
    selectedCells,
    stopMappingListData,
    stopMappingSettingsData,
    academicYearFilter,
    rowId,
    individualSaveButtonEnabled,
  ]);
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadOptionalFeeList({ ...currentOptionalFeeRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setSelectedCells([]);
    setSelectedEditRows([]);
    setSelectedRows([]);
    setIndividualSaveButtonEnabled([]);
    setBusListFilter([]);
    setAllPickupListFilter({});
    setAllDropListFilter({});
    setIndividualPickupListFilter({});
    setIndividualDropListFilter({});
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadOptionalFeeList({ ...currentOptionalFeeRequest, feeTypeId: parseInt(e.target.value, 10) });
    setSelectedCells([]);
    setSelectedEditRows([]);
    setSelectedRows([]);
    setIndividualSaveButtonEnabled([]);
    setBusListFilter([]);
    setAllPickupListFilter({});
    setAllDropListFilter({});
    setIndividualPickupListFilter({});
    setIndividualDropListFilter({});
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadOptionalFeeList({ ...currentOptionalFeeRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setSelectedCells([]);
    setSelectedCells([]);
    setSelectedEditRows([]);
    setSelectedRows([]);
    setIndividualSaveButtonEnabled([]);
    setBusListFilter([]);
    setAllPickupListFilter({});
    setAllDropListFilter({});
    setIndividualPickupListFilter({});
    setIndividualDropListFilter({});
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadOptionalFeeList({ ...currentOptionalFeeRequest, classId: parseInt(e.target.value, 10) });
    setSelectedCells([]);
    setSelectedEditRows([]);
    setSelectedRows([]);
    setIndividualSaveButtonEnabled([]);
    setBusListFilter([]);
    setAllPickupListFilter({});
    setAllDropListFilter({});
    setIndividualPickupListFilter({});
    setIndividualDropListFilter({});
  };

  const selectedIdsArray = String(selectedStudentIds || '')
    .split(',')
    .filter(Boolean)
    .map((id) => Number(id));

  const filteredData =
    selectedIdsArray.length > 0 ? studentsData.filter((row) => selectedIdsArray.includes(row.studentId)) : studentsData;

  const handleSave = useCallback(
    async (row: GetOptionalFeeStudentMapDataType) => {
      try {
        setSaving(true);
        // const feeId = row.feeId;
        const { studentId } = row;
        console.log('row::::----', row);

        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: true }));

        const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];

        const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
        const TermIds = TermList.filter(
          (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
        ).map((termId: number) => termId);

        const selectedCellsTermList = Array.from(
          new Set(selectedCells.filter((c) => c.studentId === studentId && c.termId).map((c) => c.termId))
        );

        console.log('selectedCellsTermList::::----', selectedCellsTermList);

        console.log('TermIds::::----', TermIds);

        const isSelectedRow = selectedRows.some(
          (selectedRow: StudentsMappedType) => selectedRow.studentId === row.studentId
        );

        const isSelectedEditRow = selectedEditRows.some(
          (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
        );

        // Construct feeAmount array based on section amounts
        const hasBus = !!busListFilter[studentId];
        // const pickupKey = isSelectedRow ? `${studentId}_${TermIds}` : `${studentId}_${TermIds}`;
        // const hasPickup = !!individualPickupListFilter[pickupKey];
        // const hasDrop = !!individualDropListFilter[pickupKey];

        // Loop through each TermId to ensure all pickups and drops exist
        const hasAllPickup = isSelectedRow
          ? TermIds.every((termId) => !!individualPickupListFilter[`${studentId}_${termId}`])
          : selectedCellsTermList.every((termId) => !!individualPickupListFilter[`${studentId}_${termId}`]);
        const hasAllDrop = isSelectedRow
          ? TermIds.every((termId) => !!individualDropListFilter[`${studentId}_${termId}`])
          : selectedCellsTermList.every((termId) => !!individualDropListFilter[`${studentId}_${termId}`]);
        console.log('hasAllPickup::::----', hasAllPickup);
        console.log('hasAllDrop::::----', hasAllDrop);

        if (hasBus && hasAllPickup && hasAllDrop) {
          // if (isSelectedRow && hasBus && hasPickup && hasDrop) {
          setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
          // setShowErrorPickUp((prevMap) => ({ ...prevMap, [pickupKey]: false }));
          // setShowErrorDrop((prevMap) => ({ ...prevMap, [pickupKey]: false }));

          TermIds.forEach((termId) => {
            const key = `${studentId}_${termId}`;
            setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: false }));
            setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: false }));
          });

          const stopTermMapArray: StopTermMapped[] = Object.keys(individualPickupListFilter)
            .filter((key) => parseInt(key.split('_')[0], 10) === studentId)
            .map((key) => {
              const termId = parseInt(key.split('_')[1], 10);
              const stdId = parseInt(key.split('_')[0], 10);
              const pickupId = individualPickupListFilter[`${stdId}_${termId}`];
              const dropId = individualDropListFilter[`${stdId}_${termId}`];

              return {
                feeId: pickupId,
                pickupId,
                dropId,
                termId,
                dbResult: 'string',
                stopTermMapId: 0,
              };
            })
            .filter((item) => !isNaN(item.termId) && item.pickupId !== undefined && item.dropId !== undefined);

          const sendReq: CreateStopMappingType[] = [
            {
              adminId,
              accademicId: academicYearFilter,
              sectionId: classSectionsFilter,
              classId: classFilter,
              studentId,
              feeTypeId: feeTypeFilter,
              busId: busListFilter[studentId],
              stopTermMapped: stopTermMapArray,
            },
          ];

          const actionResult = await dispatch(createStopMapping(sendReq));
          console.log('sendReq::::----', sendReq);
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [studentId]: false }));
          // setIndividualSaveButtonEnabled((prevEnabled) => ({
          //   ...prevEnabled,
          //   [row.studentId]: false,
          // }));

          if (actionResult && Array.isArray(actionResult.payload)) {
            loadOptionalFeeList({
              ...currentOptionalFeeRequest,
              academicId: academicYearFilter,
              adminId,
              sectionId: classSectionsFilter,
              classId: classFilter,
              feeTypeId: feeTypeFilter,
            });

            // setSelectedRows([]);
            // setBusListFilter([]);
            // setBusListFilter((prevBusListFilter) => prevBusListFilter.filter((cell) => !(cell === row)));
            // setSelectedRows((prevSelectedRows) => prevSelectedRows.filter((cell) => !(cell === row)));

            // Disabled Save button after saved
            Object.keys(individualPickupListFilter).map((k) => {
              const termId = parseInt(k.split('_')[1], 10);
              const stdId = parseInt(k.split('_')[0], 10);
              const cellKey = `${stdId}_${termId}`;

              return setIndividualSaveButtonEnabled((prev) => {
                const updatedEnabled: EnabledState = { ...prev };
                if (updatedEnabled[studentId]) {
                  updatedEnabled[studentId] = updatedEnabled[studentId].filter((key) => key !== cellKey);
                  if (updatedEnabled[studentId].length === 0) {
                    delete updatedEnabled[studentId]; // Remove studentId array if it becomes empty
                  }
                }
                if (updatedEnabled) {
                  const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
                  setSaveAllButtonDisabled(isSaveAllButtonDisabled);
                }
                return updatedEnabled;
              });
            });

            // Object.keys(individualPickupListFilter).map((k) => {
            //   const termId = parseInt(k.split('_')[1], 10);
            //   const stdId = parseInt(k.split('_')[0], 10);

            //   return setSelectedCells((prevSelectedCells) =>
            //     prevSelectedCells.filter((cell) => !(cell.studentId === stdId && cell.termId === termId))
            //   );
            // });

            setSelectedCells((prevSelectedCells) =>
              prevSelectedCells.filter((cell) => !(cell.studentId === row.studentId && TermIds.includes(cell.termId)))
            );

            // const updateRow = selectedRows.filter((r) => r !== row);
            // setSelectedRows(updateRow);
            // setIndividualPickupListFilter({});
            // setAllPickupListFilter((prevMap) => ({ ...prevMap, [`${row.studentId}_`]: {} }));
            // setAllDropListFilter((prevMap) => ({ ...prevMap, [`${row.studentId}_`]: {} }));
            // setIndividualPickupListFilter((prevMap) => ({ ...prevMap, [row.studentId]: {} }));
            // setIndividualDropListFilter((prevMap) => ({ ...prevMap, [row.studentId]: {} }));

            const removeStudentKeys = (stdId: number) => {
              setAllPickupListFilter((prev) =>
                Object.entries(prev).reduce((acc, [key, value]) => {
                  if (!key.startsWith(`${stdId}_`)) {
                    acc[key] = value;
                  }
                  return acc;
                }, {} as typeof prev)
              );

              setAllDropListFilter((prev) =>
                Object.entries(prev).reduce((acc, [key, value]) => {
                  if (!key.startsWith(`${stdId}_`)) {
                    acc[key] = value;
                  }
                  return acc;
                }, {} as typeof prev)
              );

              setIndividualPickupListFilter((prev) =>
                Object.entries(prev).reduce((acc, [key, value]) => {
                  if (!key.startsWith(`${stdId}_`)) {
                    acc[key] = value;
                  }
                  return acc;
                }, {} as typeof prev)
              );

              setIndividualDropListFilter((prev) =>
                Object.entries(prev).reduce((acc, [key, value]) => {
                  if (!key.startsWith(`${stdId}_`)) {
                    acc[key] = value;
                  }
                  return acc;
                }, {} as typeof prev)
              );
            };

            removeStudentKeys(row.studentId);

            console.log('removeStudentKeys::::----', removeStudentKeys);
            console.log('allPickupListFilter::::----', allPickupListFilter);
            console.log('allDropListFilter::::----', allDropListFilter);

            setBusListFilter((prevMap) => ({ ...prevMap, [row.studentId]: 0 }));
            // setAllPickupListFilter((prevMap) => ({ ...prevMap, [row.studentId]: {} }));
            setSelectedRows((prev) => prev.filter((r) => r.studentId !== row.studentId));
            setSelectedEditRows((prev) => prev.filter((r) => r !== row));

            const results = actionResult.payload;
            console.log('results::::----', results);
            // Filter out items without termAmount property
            const filteredResults = results.filter((f) => f.stopTermMapped);
            console.log('filteredResults::::----', filteredResults);

            // Extract termAmount arrays
            const stopTermMappedArray = filteredResults.map((f) => f.stopTermMapped);

            // Flatten the array of termAmount arrays
            const stopTermMappedArr = stopTermMappedArray.flat();
            console.log('stopTermMappedArr::::----', stopTermMappedArr);

            // Find the item with dbResult === 'Success'
            const SuccessResult = stopTermMappedArr.find((f) => f.dbResult === 'Success');
            console.log('SuccessResult::::----', SuccessResult);
            if (SuccessResult) {
              setSuccesResponse(SuccessResult.dbResult);
            }

            // Find the item with dbResult === 'Updated'
            const UpdateResult = stopTermMappedArr.find((f) => f.dbResult === 'Updated');
            console.log('UpdateResult::::----', UpdateResult);
            if (UpdateResult) {
              setSuccesResponse(UpdateResult.dbResult);
            }
            setSaving(false);
            // setTermFeeDetails(response);
            setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));

            setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: true }));
            setTimeout(() => {
              setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: false }));
            }, 5000);
          }
          // }
        } else {
          // Set individual save loading to false early
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [studentId]: false }));

          // Determine which specific errors to show
          if (!hasBus) {
            // Bus is missing: show only bus error
            setShowError((prevMap) => ({ ...prevMap, [studentId]: true }));
            // Bus is present but pickup or drop is missing
            TermIds.forEach((termId) => {
              const key = `${studentId}_${termId}`;
              if (!hasAllPickup) {
                setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: true }));
              }
              if (!hasAllDrop) {
                setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: true }));
              }

              let countdown = 5;
              setErrorCountdownMap((prevMap) => ({ ...prevMap, [key]: countdown }));

              const intervalId = setInterval(() => {
                countdown -= 1;
                setErrorCountdownMap((prevMap) => ({ ...prevMap, [key]: countdown }));
                if (countdown <= 0) {
                  clearInterval(intervalId);
                  setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
                  setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: false }));
                  setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: false }));
                  setErrorCountdownMap((prevMap) => {
                    const updated = { ...prevMap };
                    delete updated[key];
                    return updated;
                  });
                }
              }, 1000);

              setTimeout(() => {
                setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
                setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: false }));
                setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: false }));
              }, 5000);
            });
          } else {
            // Bus is present but pickup or drop is missing
            TermIds.forEach((termId) => {
              const key = `${studentId}_${termId}`;
              if (!hasAllPickup) {
                setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: true }));
              }
              if (!hasAllDrop) {
                setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: true }));
              }

              let countdown = 5;
              setErrorCountdownMap((prevMap) => ({ ...prevMap, [key]: countdown }));

              const intervalId = setInterval(() => {
                countdown -= 1;
                setErrorCountdownMap((prevMap) => ({ ...prevMap, [key]: countdown }));
                if (countdown <= 0) {
                  clearInterval(intervalId);
                  setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
                  setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: false }));
                  setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: false }));
                  setErrorCountdownMap((prevMap) => {
                    const updated = { ...prevMap };
                    delete updated[key];
                    return updated;
                  });
                }
              }, 1000);

              setTimeout(() => {
                setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
                setShowErrorPickUp((prevMap) => ({ ...prevMap, [key]: false }));
                setShowErrorDrop((prevMap) => ({ ...prevMap, [key]: false }));
              }, 5000);
            });

            // setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));
            // setShowError((prevMap) => ({ ...prevMap, [row.studentId]: true }));
            // setShowErrorPickUp((prevMap) => ({ ...prevMap, [`${studentId}_${TermIds[0]}`]: true }));
            // setShowErrorDrop((prevMap) => ({ ...prevMap, [`${studentId}_${TermIds[0]}`]: true }));

            console.error('bus name required error');
            console.log('bus name required error', individualPickupListFilter[`${row.studentId}_5`]);
            console.log('bus name required error', busListFilter[`${row.studentId}`]);
          }
        }
      } catch (error) {
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));
        // Handle errors here
        // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
      }
    },
    [
      dispatch,
      adminId,
      academicYearFilter,
      classSectionsFilter,
      classFilter,
      feeTypeFilter,
      individualPickupListFilter,
      individualDropListFilter,
      busListFilter,
      loadOptionalFeeList,
      currentOptionalFeeRequest,
      stopMappingListData,
      selectedRows,
      selectedCells,
      selectedEditRows,
      allPickupListFilter,
      allDropListFilter,
    ]
  );

  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);

      // const studentIdArray = Object.keys(individualPickupListFilter).map((key) => parseInt(key.split('_')[0], 10));
      const selectedStudentIdsSet = new Set(selectedRows.map((row) => row.studentId));

      const studentIdArray = Object.keys(individualPickupListFilter)
        .map((key) => parseInt(key.split('_')[0], 10))
        .filter((id) => selectedStudentIdsSet.has(id));

      // Ensure studentIdArray contains unique values
      if (studentIdArray.length === 0) {
        setSavingAll(false);
        const deleteErrorMessage = (
          <DeleteMessage loop={false} jsonIcon={errorIcon} message="Please select field required" />
        );
        await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
      } else {
        const uniqueStudentIds = [...new Set(studentIdArray)];
        await Promise.all(
          uniqueStudentIds.map(async (studentId) => {
            if (busListFilter[studentId]) {
              // Construct feeAmount array based on section amounts
              const stopTermMapArray: StopTermMapped[] = Object.keys(individualPickupListFilter)
                .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where feeId matches row.feeId
                .map((key) => {
                  const termId = parseInt(key.split('_')[1], 10);
                  const stdId = parseInt(key.split('_')[0], 10);
                  return {
                    feeId: individualPickupListFilter[`${stdId}_${termId}`],
                    pickupId: individualPickupListFilter[`${stdId}_${termId}`],
                    dropId: individualDropListFilter[`${stdId}_${termId}`],
                    termId,
                    dbResult: 'string',
                    stopTermMapId: 0,
                  };
                });

              const sendReq = [
                {
                  adminId,
                  accademicId: academicYearFilter,
                  academicFeeId: 0,
                  sectionId: classSectionsFilter,
                  classId: classFilter,
                  studentId,
                  feeTypeId: feeTypeFilter,
                  busId: busListFilter[studentId],
                  stopTermMapped: stopTermMapArray,
                },
              ];
              const actionResult = await dispatch(createStopMapping(sendReq));

              if (actionResult && Array.isArray(actionResult.payload)) {
                setSelectedRows([]);
                setBusListFilter([]);
                setSelectedCells([]);
                setSelectedEditRows([]);
                setBusListFilter([]);
                setIndividualPickupListFilter({});
                setIndividualDropListFilter({});
                setAllPickupListFilter({});
                setAllDropListFilter({});
                setSnackBar(true);
                const results = actionResult.payload;
                console.log('results::::----', results);
                // Filter out items without termAmount property
                const filteredResults = results.filter((f) => f.stopTermMapped);
                console.log('filteredResults::::----', filteredResults);

                // Extract termAmount arrays
                const stopTermMappedArray = filteredResults.map((f) => f.stopTermMapped);

                // Flatten the array of termAmount arrays
                const stopTermMappedArr = stopTermMappedArray.flat();
                console.log('stopTermMappedArr::::----', stopTermMappedArr);

                // Find the item with dbResult === 'Success'
                const SuccessResult = stopTermMappedArr.find((f) => f.dbResult === 'Success');
                console.log('SuccessResult::::----', SuccessResult);
                if (SuccessResult) {
                  setSuccesResponse(SuccessResult.dbResult);
                }

                // Find the item with dbResult === 'Updated'
                const UpdateResult = stopTermMappedArr.find((f) => f.dbResult === 'Updated');
                console.log('UpdateResult::::----', UpdateResult);
                if (UpdateResult) {
                  setSuccesResponse(UpdateResult.dbResult);
                }
                setSavingAll(false);
                setIndividualSaveButtonEnabled([]);
                setSaveAllButtonDisabled(true);

                loadOptionalFeeList({
                  ...currentOptionalFeeRequest,
                  academicId: academicYearFilter,
                  adminId,
                  sectionId: classSectionsFilter,
                  classId: classFilter,
                });
              }
            } else {
              setErrorSaveAll(true);
              setSavingAll(false);
              // const deleteErrorMessage = (
              //   <DeleteMessage loop={false} jsonIcon={errorIcon} message="Please select bus name" />
              // );
              // await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
              setShowError((prevMap) => ({ ...prevMap, [studentId]: true }));
              setTimeout(() => {
                setShowError((prevMap) => ({ ...prevMap, [studentId]: false }));
              }, 5000);
            }
          })
        );
      }
    } catch (error) {
      setSavingAll(false);
      // Handle errors here
      // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    }
  }, [
    dispatch,
    adminId,
    academicYearFilter,
    classSectionsFilter,
    classFilter,
    currentOptionalFeeRequest,
    loadOptionalFeeList,
    feeTypeFilter,
    individualPickupListFilter,
    individualDropListFilter,
    busListFilter,
    confirm,
    selectedRows,
  ]);
  type RowIds = {
    [key: string]: number;
  };
  type EnabledState = {
    [studentId: string]: string[];
  };
  const handleCellClick = useCallback(
    async (studentId: number, termId: number) => {
      const cellKey = `${studentId}_${termId}`;

      // Check if cell is already selected
      const cellIndex = selectedCells.findIndex((cell) => cell.studentId === studentId && cell.termId === termId);
      // const cellIndex = Array.isArray(selectedRows)
      //   ? selectedRows.findIndex(
      //       (selectedRow) =>
      //         selectedRow.studentId === studentId &&
      //         Array.isArray(selectedRow.stopTermMapped) &&
      //         selectedRow.stopTermMapped.some((term) => term.termId === termId)
      //     )
      //   : -1;

      if (cellIndex !== -1) {
        // Cell a lready selected, deselect it
        setSelectedCells((prevSelectedCells) =>
          prevSelectedCells.filter((cell) => !(cell.studentId === studentId && cell.termId === termId))
        );

        setRowId((prev) => {
          const updatedIds: RowIds = { ...prev };
          delete updatedIds[cellKey];
          return updatedIds;
        });
        console.log('rowId::::----', rowId);
        setIndividualSaveButtonEnabled((prev) => {
          const updatedEnabled: EnabledState = { ...prev };
          if (updatedEnabled[studentId]) {
            updatedEnabled[studentId] = updatedEnabled[studentId].filter((key) => key !== cellKey);
            if (updatedEnabled[studentId].length === 0) {
              delete updatedEnabled[studentId]; // Remove studentId array if it becomes empty
            }
          }
          if (updatedEnabled) {
            const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
            setSaveAllButtonDisabled(isSaveAllButtonDisabled);
          }
          return updatedEnabled;
        });
      } else {
        setSelectedCells((prevSelectedCells) => [...prevSelectedCells, { studentId, termId }]);
        // Cell not selected, select it
        setRowId((prev) => {
          const updatedIds: RowIds = { ...prev };
          updatedIds[cellKey] = cellKey;
          return updatedIds;
        });
        setIndividualSaveButtonEnabled((prevEnabled) => {
          const updatedEnabled = { ...prevEnabled };
          if (!updatedEnabled[studentId]) {
            updatedEnabled[studentId] = []; // Initialize as an empty array if not exists
          }
          if (!updatedEnabled[studentId].includes(cellKey)) {
            updatedEnabled[studentId].push(cellKey); // Add cellKey to the array
            if (updatedEnabled) {
              const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
              setSaveAllButtonDisabled(isSaveAllButtonDisabled);
            }
          }
          return updatedEnabled;
        });
        // Enable the Save All button
      }
      // Check if any cells are selected
    },
    [rowId, selectedCells]
  );

  const handleEditRowClick = useCallback(
    (row: any) => {
      const selectedIndex = selectedEditRows?.indexOf(row);
      let newSelected: any[] = [];

      const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
      const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
      const TermIds = TermList.filter(
        (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
      ).map((termId: number) => termId);

      setSelectedCells((prevSelectedCells) =>
        prevSelectedCells.filter((cell) => !(cell.studentId === row.studentId && TermIds.includes(cell.termId)))
      );

      console.log('selectedCells::::----', selectedCells);

      if (selectedIndex === -1) {
        newSelected = [...selectedEditRows, row];
      } else if (selectedIndex === 0) {
        newSelected = selectedEditRows.slice(1);
      } else if (selectedIndex === selectedEditRows.length - 1) {
        newSelected = selectedEditRows.slice(0, -1);
      } else if (selectedIndex > 0) {
        newSelected = [...selectedEditRows.slice(0, selectedIndex), ...selectedEditRows.slice(selectedIndex + 1)];
      }

      setSelectedEditRows(newSelected);
    },
    [selectedEditRows, setSelectedEditRows, stopMappingListData, selectedCells]
  );

  const handleDeleteCell = useCallback(
    async (stopTermMapId: number | undefined) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            stopTermMapId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteBusMapped(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadOptionalFeeList(currentOptionalFeeRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadOptionalFeeList(currentOptionalFeeRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, loadOptionalFeeList, currentOptionalFeeRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: GetStopTermMappedType) => {
      const { studentId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId,
            dbResult: 'string',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllBusMappedStudent(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadOptionalFeeList(currentOptionalFeeRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadOptionalFeeList(currentOptionalFeeRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, classFilter, theme, loadOptionalFeeList, currentOptionalFeeRequest]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadOptionalFeeList(initialOptionalFeeRequest);
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setFeeTypeFilter(0);
      setClassFilter(0);
      setSelectedStudentIds('');
      // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
    },
    [initialOptionalFeeRequest, loadOptionalFeeList]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: DeleteAllBusMappedStudentType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId: row.studentId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllBusMappedStudent(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteAllBusMappedStudentType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          loadOptionalFeeList(currentOptionalFeeRequest);
          setSelectedRows([]);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadOptionalFeeList(currentOptionalFeeRequest);
        // setSelectedRows([]);
        // setTermFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item)));
      }
    }
  }, [
    confirm,
    dispatch,
    adminId,
    selectedRows,
    academicYearFilter,
    classFilter,
    loadOptionalFeeList,
    currentOptionalFeeRequest,
  ]);

  const isSelected = useCallback((row: StudentsMappedType) => selectedRows?.indexOf(row) !== -1, [selectedRows]);

  const handleRowClick = useCallback(
    (row: StudentsMappedType) => {
      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: StudentsMappedType[] = [];

      const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
      const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
      const TermIds = TermList.filter(
        (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
      ).map((termId: number) => termId);

      console.log('selectedCells::::----', selectedCells);

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selectedRows, row);

        // Add { studentId, termId } to selectedCells
        setSelectedCells((prevSelectedCells) => {
          const newCells = TermIds.map((termId: number) => ({
            studentId: row.studentId,
            termId,
          }));

          // Avoid duplicates
          const cellExists = (cell: any) =>
            prevSelectedCells.some(
              (existing) => existing.studentId === cell.studentId && existing.termId === cell.termId
            );

          return [...prevSelectedCells, ...newCells.filter((cell) => !cellExists(cell))];
        });
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selectedRows.slice(1));
        setSelectedCells((prevSelectedCells) =>
          prevSelectedCells.filter((cell) => !(cell.studentId === row.studentId && TermIds.includes(cell.termId)))
        );
      } else if (selectedIndex === selectedRows.length - 1) {
        newSelected = newSelected.concat(selectedRows.slice(0, -1));
        setSelectedCells((prevSelectedCells) =>
          prevSelectedCells.filter((cell) => !(cell.studentId === row.studentId && TermIds.includes(cell.termId)))
        );
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
        // Remove { studentId, termId } from selectedCells
        setSelectedCells((prevSelectedCells) =>
          prevSelectedCells.filter((cell) => !(cell.studentId === row.studentId && TermIds.includes(cell.termId)))
        );
      }

      setSelectedRows(newSelected);
    },
    [selectedRows, selectedCells, stopMappingListData]
  );

  const handleRowAllClick = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        const TermListLength = stopMappingListData?.termList?.length || 0;

        const selectableRows = studentsData.filter((row) => (row.stopTermMapped?.length || 0) < TermListLength);

        setSelectedRows(selectableRows);
      } else {
        setSelectedRows([]);
      }
    },
    [studentsData, stopMappingListData, setSelectedRows]
  );

  const [studentNameSortOrder, setStudentNameSortOrder] = useState<'asc' | 'desc' | null>('asc');
  const [admissionNoSortOrder, setAdmissionNoSortOrder] = useState<'asc' | 'desc' | null>(null);
  const [studentNameToolbarSortLabel, setStudentNameToolbarSortLabel] =
    useState<string>('Sorted by Student Name (ASC)');
  const [admNoToolbarSortLabel, setAdmNoToolbarSortLabel] = useState<string>('Sorted by Admission Number (ASC)');

  // --- sorted data (priority: admission if active, else studentName)
  const sortedData = useMemo(() => {
    if (admissionNoSortOrder) {
      return [...filteredData].sort((a, b) => {
        const numA = Number(a.admissionNo) || 0;
        const numB = Number(b.admissionNo) || 0;
        return admissionNoSortOrder === 'asc' ? numA - numB : numB - numA;
      });
    }
    if (studentNameSortOrder) {
      return [...filteredData].sort((a, b) => {
        const nameA = (a.studentName || '').toLowerCase();
        const nameB = (b.studentName || '').toLowerCase();
        return studentNameSortOrder === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
      });
    }
    return filteredData;
  }, [filteredData, admissionNoSortOrder, studentNameSortOrder]);

  const [isFullScreen, setIsFullScreen] = useState(false);

  const toggleFullScreen = () => {
    setIsFullScreen((prev) => !prev);
  };

  const OptionalFeeSettingListColumns: DataTableColumn<StudentsMappedType>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'selection',
        renderHeader: () => {
          return (
            <Checkbox
              sx={{
                color: theme.palette.common.white,
                '&.Mui-checked': {
                  color: theme.palette.common.white,
                },
                '&.MuiCheckbox-indeterminate': {
                  color: theme.palette.common.white,
                },
              }}
              onChange={handleRowAllClick}
              indeterminate={selectedRows?.length > 0 && selectedRows?.length < studentsData.length}
              checked={selectedRows?.length > 0}
            />
          );
        },
        renderCell: (row) => {
          const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
          const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
          const isFullyMapped = stopTermMapped.length === TermList.length;
          const isSelectedRow =
            Array.isArray(selectedRows) &&
            selectedRows.some((selectedRow: StudentsMappedType) => selectedRow.studentId === row.studentId);
          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );
          const isRowSelected = isSelected(row);
          return (
            <Stack
              alignItems="center"
              justifyContent="center"
              borderRight={1}
              borderColor={theme.palette.common.white}
              height={62}
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
            >
              <Checkbox
                sx={{
                  color: isSelectedRow ? theme.palette.common.white : '',
                  '&.Mui-checked': {
                    color: theme.palette.common.white,
                  },
                  '&.MuiCheckbox-indeterminate': {
                    color: theme.palette.common.white,
                  },
                }}
                checked={isRowSelected}
                disabled={isFullyMapped}
                onClick={() => handleRowClick(row)}
              />
            </Stack>
          );
        },
      },
      {
        name: 'admissionNo',
        // renderHeader: () => (
        //   <Typography sx={{ minWidth: 70 }} variant="subtitle2" fontSize={13}>
        //     Adm No
        //   </Typography>
        // ),
        renderHeader: () => (
          <Tooltip title={admNoToolbarSortLabel} placement="top">
            <Stack
              direction="row"
              alignItems="center"
              sx={{ cursor: 'pointer' }}
              onClick={() => {
                setAdmissionNoSortOrder((prev) => {
                  const newOrder = prev === 'asc' ? 'desc' : 'asc';
                  setAdmNoToolbarSortLabel(`Sorted by Admission No (${newOrder.toUpperCase()})`);
                  setStudentNameSortOrder(null); // turn off other sort so only one active
                  return newOrder;
                });
              }}
            >
              <Typography sx={{ minWidth: 70 }} variant="subtitle2" fontSize={13}>
                Adm No
              </Typography>
              <Stack position="absolute" right={0}>
                {admissionNoSortOrder === 'asc' && <ArrowUpward fontSize="small" />}
                {admissionNoSortOrder === 'desc' && <ArrowDownward fontSize="small" />}
              </Stack>
            </Stack>
          </Tooltip>
        ),
        renderCell: (row) => {
          const isSelectedRow =
            Array.isArray(selectedRows) && selectedRows.some((selectedRow) => selectedRow.studentId === row.studentId);

          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );
          return (
            <Stack
              borderRight={1}
              borderColor={theme.palette.common.white}
              alignItems="center"
              justifyContent="center"
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
              height={62}
              minWidth={70}
            >
              <Typography color={isSelectedRow ? 'white' : ''} variant="subtitle1" fontSize={13}>
                {row.admissionNo}
              </Typography>
            </Stack>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        // renderHeader: () => (
        //   <Typography minWidth={200} variant="subtitle2" fontSize={13}>
        //     Student Name
        //   </Typography>
        // ),
        renderHeader: () => (
          <Tooltip title={studentNameToolbarSortLabel} placement="top">
            <Stack
              direction="row"
              alignItems="center"
              sx={{ cursor: 'pointer' }}
              onClick={() => {
                setStudentNameSortOrder((prev) => {
                  const newOrder = prev === 'asc' ? 'desc' : 'asc';
                  setStudentNameToolbarSortLabel(`Sorted by Student Name (${newOrder.toUpperCase()})`);
                  setAdmissionNoSortOrder(null); // disable admission sort
                  return newOrder;
                });
              }}
            >
              <Typography minWidth={200} variant="subtitle2" fontSize={13}>
                Student Name
              </Typography>
              <Stack position="absolute" right={10}>
                {studentNameSortOrder === 'asc' && <ArrowUpward fontSize="small" />}
                {studentNameSortOrder === 'desc' && <ArrowDownward fontSize="small" />}
              </Stack>
            </Stack>
          </Tooltip>
        ),
        renderCell: (row) => {
          const isSelectedRow =
            Array.isArray(selectedRows) && selectedRows.some((selectedRow) => selectedRow.studentId === row.studentId);

          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );
          return (
            <Stack
              borderRight={1}
              borderColor={theme.palette.common.white}
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
              height={62}
              minWidth={200}
              pl={1}
              direction="row"
              alignItems="center"
              gap={1}
            >
              <Avatar src="" />
              <Typography color={isSelectedRow ? 'white' : ''} variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'busName',
        renderHeader: () => (
          <Typography width={110} variant="subtitle2" fontSize={13}>
            Bus Name
          </Typography>
        ),
        renderCell: (row) => {
          const isSelectedRow =
            Array.isArray(selectedRows) && selectedRows.some((selectedRow) => selectedRow.studentId === row.studentId);

          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );

          return (
            <Stack
              borderRight={1}
              borderColor={theme.palette.common.white}
              px={1}
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
              height={62}
              alignContent="center"
              justifyContent="center"
            >
              <Select
                // disabled={!isSelectedRow}
                labelId="busListFilter"
                id="busListFilterSelect"
                error={showError[row.studentId]}
                // defaultValue={0}
                value={busListFilter[row.studentId] || 0}
                onChange={(e) => {
                  setShowError((prevMap) => ({ ...prevMap, [row.studentId]: false }));
                  const busId = parseInt(e.target.value as string, 10);
                  setBusListFilter((prev) => ({
                    ...prev,
                    [row.studentId]: busId,
                  }));
                }}
                placeholder="Select Bus"
                sx={{
                  height: 30,
                  minWidth: 100,
                  backgroundColor: isLight
                    ? isSelectedEditRow || isSelectedRow
                      ? theme.palette.common.white
                      : theme.palette.common.white
                    : isSelectedEditRow || isSelectedRow
                    ? theme.palette.common.black
                    : theme.palette.common.black,
                }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: '300px', // Adjust the value to your desired height
                    },
                  },
                }}
              >
                <MenuItem value={0}>Select</MenuItem>
                {busListDatas.map((opt) => (
                  <MenuItem key={opt.busId} value={opt.busId}>
                    {opt.busName}
                  </MenuItem>
                ))}
              </Select>
              {showError[row.studentId] && (
                <FormHelperText
                  sx={{
                    fontSize: 10,
                    position: 'absolute',
                    bottom: 0,
                    left: 30,
                    color: theme.palette.error.main,
                    fontWeight: 600,
                  }}
                >
                  Required
                </FormHelperText>
              )}
            </Stack>
          );
        },
      },
      {
        name: 'pickupPoint',
        renderHeader: () => (
          <Typography width={135} variant="subtitle2" fontSize={13}>
            Pickup Point
          </Typography>
        ),
        renderCell: (row) => {
          const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
          const isSelectedRow =
            Array.isArray(selectedRows) &&
            selectedRows.some((selectedRow: StudentsMappedType) => selectedRow.studentId === row.studentId);
          const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
          const TermIds = TermList.filter(
            (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
          ).map((termId: number) => termId);

          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );

          return (
            <Stack
              borderRight={1}
              borderColor={theme.palette.common.white}
              px={1}
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
              height={62}
              alignContent="center"
              justifyContent="center"
            >
              <Autocomplete
                clearIcon={false}
                disableClearable
                disabled={!isSelectedEditRow && !isSelectedRow}
                options={pickupDropListDatas}
                getOptionLabel={(option) => option.feeTitle || ''}
                isOptionEqualToValue={(option, value) => option.feeId === value.feeId}
                value={pickupDropListDatas.find((opt) => opt.feeId === allPickupListFilter[`${row.studentId}_`])}
                error={isSelectedRow && showErrorPickUp[`${row.studentId}_${TermIds[0]}`]}
                onChange={(_, newValue) => {
                  setShowErrorPickUp((prevMap) => ({ ...prevMap, [`${row.studentId}_${TermIds[0]}`]: false }));
                  setShowErrorDrop((prevMap) => ({ ...prevMap, [`${row.studentId}_${TermIds[0]}`]: false }));
                  const feeId = newValue?.feeId || 0;

                  setAllPickupListFilter((prev) => ({
                    ...prev,
                    [`${row.studentId}`]: feeId,
                  }));
                  setAllDropListFilter((prev) => ({
                    ...prev,
                    [`${row.studentId}`]: feeId,
                  }));

                  if (isSelectedRow) {
                    TermList.filter(
                      (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
                    ).forEach((termId: number) => {
                      setIndividualPickupListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                      setIndividualDropListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                    });
                  }

                  if (isSelectedEditRow) {
                    TermList.filter((termId: number) =>
                      stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
                    ).forEach((termId: number) => {
                      setIndividualPickupListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                      setIndividualDropListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                    });
                  }
                }}
                componentsProps={{
                  paper: {
                    sx: {
                      width: 250,
                    },
                  },
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select"
                    size="small"
                    InputProps={{
                      ...params.InputProps,
                      sx: {
                        backgroundColor:
                          isSelectedRow || isSelectedEditRow
                            ? isLight
                              ? theme.palette.common.white
                              : theme.palette.common.black
                            : isLight
                            ? theme.palette.grey[200]
                            : theme.palette.grey[800],
                        '& .MuiInputBase-input': {
                          height: '19px',
                          padding: '0 8px',
                          boxSizing: 'border-box',
                        },
                      },
                    }}
                  />
                )}
                sx={{
                  width: 120,
                }}
              />
              {isSelectedRow && showErrorPickUp[`${row.studentId}_${TermIds[0]}`] && (
                <FormHelperText
                  sx={{
                    fontSize: 10,
                    position: 'absolute',
                    bottom: 0,
                    left: 30,
                    color: theme.palette.error.main,
                    fontWeight: 600,
                  }}
                >
                  Required
                </FormHelperText>
              )}
            </Stack>
          );
        },
      },
      {
        name: ' dropPoint',
        renderHeader: () => (
          <Typography variant="subtitle2" fontSize={13}>
            Drop Point
          </Typography>
        ),
        renderCell: (row) => {
          const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
          const isSelectedRow =
            Array.isArray(selectedRows) && selectedRows.some((selectedRow) => selectedRow.studentId === row.studentId);
          const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
          const TermIds = TermList.filter(
            (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
          ).map((termId: number) => termId);

          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );
          return (
            <Stack
              borderRight={1}
              borderColor={theme.palette.common.white}
              px={1}
              bgcolor={isSelectedRow ? '#51a0c2' : isSelectedEditRow ? theme.palette.warning.light : ''}
              height={62}
              alignContent="center"
              justifyContent="center"
            >
              <Autocomplete
                clearIcon={false}
                disableClearable
                disabled={!isSelectedEditRow && !isSelectedRow}
                options={pickupDropListDatas}
                getOptionLabel={(option) => option.feeTitle || ''}
                isOptionEqualToValue={(option, value) => option.feeId === value.feeId}
                value={pickupDropListDatas.find(
                  (opt) =>
                    opt.feeId === allDropListFilter[`${row.studentId}`] || allPickupListFilter[`${row.studentId}`]
                )}
                onChange={(_, newValue) => {
                  setShowErrorDrop((prevMap) => ({ ...prevMap, [`${row.studentId}_${TermIds[0]}`]: false }));
                  const feeId = newValue?.feeId || 0;

                  setAllDropListFilter((prev) => ({
                    ...prev,
                    [`${row.studentId}`]: feeId,
                  }));

                  if (isSelectedRow) {
                    TermList.filter(
                      (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
                    ).forEach((termId: number) => {
                      setIndividualDropListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                    });
                  }
                  if (isSelectedEditRow) {
                    TermList.filter((termId: number) =>
                      stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
                    ).forEach((termId: number) => {
                      setIndividualDropListFilter((prev) => ({
                        ...prev,
                        [`${row.studentId}_${termId}`]: feeId,
                      }));
                    });
                  }
                }}
                componentsProps={{
                  paper: {
                    sx: {
                      width: 250,
                    },
                  },
                }}
                error={isSelectedRow && showErrorDrop[`${row.studentId}_${TermIds[0]}`]}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select"
                    size="small"
                    InputProps={{
                      ...params.InputProps,
                      sx: {
                        // height: 30,
                        backgroundColor:
                          isSelectedRow || isSelectedEditRow
                            ? isLight
                              ? theme.palette.common.white
                              : theme.palette.common.black
                            : isLight
                            ? theme.palette.grey[200]
                            : theme.palette.grey[800],
                        '& .MuiInputBase-input': {
                          height: '19px',
                          padding: '0 8px',
                          boxSizing: 'border-box',
                        },
                      },
                    }}
                  />
                )}
                sx={{
                  width: 120,
                }}
              />
              {isSelectedRow && showErrorDrop[`${row.studentId}_${TermIds[0]}`] && (
                <FormHelperText
                  sx={{
                    fontSize: 10,
                    position: 'absolute',
                    bottom: 0,
                    left: 30,
                    color: theme.palette.error.main,
                    fontWeight: 600,
                  }}
                >
                  Required
                </FormHelperText>
              )}
            </Stack>
          );
        },
      },
    ];

    const headerAndBodyCells = stopMappingListData.termList
      ? stopMappingListData.termList.map((item: { termId: number; termTitle: string }) => ({
          name: `${item.termId}`,
          // width: '80px',
          renderHeader: () => (
            <Stack minWidth={300} key={item.termId} className="header-color" direction="row" justifyContent="center">
              <Typography variant="subtitle2" fontSize={12}>
                {item.termTitle}
              </Typography>
            </Stack>
          ),
          renderCell: (row: StudentsMappedType) => {
            const studentIds = row.stopTermMapped ? row.stopTermMapped.map((i) => i.studentId) : [];
            const termIds = row.stopTermMapped ? row.stopTermMapped.map((i) => i.termId) : [];
            const stopTermMapId = row.stopTermMapped?.find(
              (i) => i.termId === item.termId && i.studentId === row.studentId
            )?.stopTermMapId;

            const stopTermMapped = row.stopTermMapped || [];

            const mappedEntry = stopTermMapped.find((mapped) => mapped.termId === item.termId);

            const pickupPointTitle = pickupDropListDatas.find(
              (f) => f.feeId === (mappedEntry?.pickupId ?? 0)
            )?.feeTitle;

            const pickupPointId = pickupDropListDatas.find((f) => f.feeId === (mappedEntry?.pickupId ?? 0))?.feeId || 0;

            const dropPointTitle = pickupDropListDatas.find((f) => f.feeId === (mappedEntry?.dropId ?? 0))?.feeTitle;
            const dropPointId = pickupDropListDatas.find((f) => f.feeId === (mappedEntry?.dropId ?? 0))?.feeId || 0;

            const isSavedCellStudent = studentIds.includes(row.studentId) && termIds.includes(item.termId);
            const studentMappedData = studentIds.includes(row.studentId) && termIds.includes(item.termId).optionalMapId;
            // console.log('optionalMapId::::----', optionalMapId);

            const isSelectedEditRow = selectedEditRows.some(
              (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
            );
            // const editableCells = selectedRows.includes(row.studentId);
            const isSelectedRow =
              Array.isArray(selectedRows) &&
              selectedRows.some(
                (selectedRow: StudentsMappedType) => selectedRow.studentId === row.studentId
                // &&
                //   Array.isArray(selectedRow.stopTermMapped) &&
                //   selectedRow.stopTermMapped.some((term) => term.termId === item.termId)
              );

            // console.log('isSelectedRow::::----', isSelectedRow);

            // const disbledCheckBox

            const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
            const TermIds = TermList.filter(
              (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
            ).map((termId: number) => termId);

            const selectedCell = selectedCells.some(
              (cell) => cell.studentId === row.studentId && cell.termId === item.termId
            );

            return (
              <Button
                disabled={!isSavedCellStudent && isSelectedEditRow}
                disableRipple={selectedCell}
                key={`cell_${row.studentId}_${item.termId}`}
                sx={{
                  p: 0,
                  width: '100%',
                  height: '100%',
                  borderRadius: 0,
                  borderRight: 1,
                  borderColor: theme.palette.grey[100],
                }}
                color="info"
              >
                <Stack
                  // onClick={() => {
                  //   if (!isSavedCellStudent) handleCellClick(row.studentId, item.feeId);
                  // }}
                  className={selectedCell ? 'hoveredCell' : ''}
                  sx={{
                    px: 1,
                    overflow: 'hidden',
                    position: 'relative',
                    background:
                      isSavedCellStudent && !isSelectedEditRow
                        ? theme.palette.success.lighter
                        : isSavedCellStudent && isSelectedEditRow
                        ? theme.palette.warning.light
                        : selectedCell || (isSelectedRow && !isSavedCellStudent)
                        ? '#51a0c2'
                        : !isSavedCellStudent && isSelectedEditRow
                        ? theme.palette.grey[400]
                        : '',
                    // background:
                    //   !isSelectedRow && isSavedCellStudent
                    //     ? theme.palette.success.lighter
                    //     : isSelectedRow && isSavedCellStudent
                    //     ? theme.palette.warning.light
                    //     : selectedCell || isSelectedRow
                    //     ? theme.palette.info.light
                    //     : '',
                    '&:hover': {
                      background: !isSavedCellStudent && !selectedCells ? theme.palette.info.lighter : '',
                      transition: '0.5s',
                    },
                    // width: '10%',
                    '.hoveredCell:hover &': {
                      // width: '100%',
                    },
                  }}
                  width="100%"
                  height="100%"
                  direction="row"
                  justifyContent="center"
                  // gap={2}
                  // py={1}
                >
                  {selectedCell && !isSelectedRow && (
                    <>
                      {/* Red background bar */}
                      {/* <Stack
                        bgcolor={theme.palette.info.light}
                        height="100%"
                        width={35}
                        // borderRight={1}
                        // borderTop={1}
                        // borderBottom={1}
                        // borderColor={theme.palette.grey[200]}
                        sx={{
                          position: 'absolute',
                          right: 0,
                          zIndex: 1,
                          opacity: 0,
                          transform: 'translateX(10px)',
                          transition: 'opacity 0.3s ease, transform 0.3s ease',
                          pointerEvents: 'none',
                          '.hoveredCell:hover &': {
                            opacity: 1,
                            transform: 'translateX(0)',
                            pointerEvents: 'auto',
                          },
                        }}
                      /> */}

                      {/* Radio icon on top of red bar */}
                      <Radio
                        color="error"
                        checked={isSelectedRow || selectedCell}
                        size="small"
                        onClick={() => {
                          if (!isSelectedRow && selectedCell) handleCellClick(row.studentId, item.termId);
                        }}
                        checkedIcon={
                          <CloseIcon
                            sx={{
                              color:
                                isSelectedRow && isSavedCellStudent
                                  ? theme.palette.common.white
                                  : theme.palette.common.white,
                            }}
                          />
                        }
                        sx={{
                          position: 'absolute',
                          right: 2,
                          top: 16,
                          zIndex: 1, // Must be above the red Stack
                          opacity: 0,
                          transform: 'translateX(10px)',
                          transition: 'opacity 0.3s ease, transform 0.3s ease',
                          pointerEvents: 'none',
                          '.hoveredCell:hover &': {
                            opacity: 1,
                            transform: 'translateX(0)',
                            pointerEvents: 'auto',
                          },
                        }}
                        disableRipple={isSelectedRow}
                      />
                    </>
                  )}

                  {!isSelectedEditRow && isSavedCellStudent && (
                    <Grid container minHeight={62} alignItems="center">
                      <Grid item xs={5.5} sx={{ borderRight: 0, borderColor: theme.palette.success.main }}>
                        <Typography fontSize={10} color={theme.palette.common.black} variant="subtitle1" m={0}>
                          Pickup Point
                        </Typography>
                        <Tooltip title={pickupPointTitle} placement="top">
                          <Typography className="pointTitle" variant="subtitle2" color={theme.palette.success.main}>
                            {pickupPointTitle}
                          </Typography>
                        </Tooltip>
                      </Grid>
                      {/* <HorizontalRuleIcon sx={{ mt: 2 }} /> */}
                      {/* &ndash; */}
                      <div style={{ height: '100%', minWidth: '1px', backgroundColor: theme.palette.success.main }}>
                        {' '}
                      </div>
                      <Grid item xs={5.5}>
                        <Typography fontSize={10} color={theme.palette.common.black} variant="subtitle1" m={0}>
                          Drop Point
                        </Typography>
                        <Tooltip title={dropPointTitle} placement="top">
                          <Typography className="pointTitle" variant="subtitle2" color={theme.palette.success.main}>
                            {dropPointTitle}
                          </Typography>
                        </Tooltip>
                      </Grid>
                      <IconButton
                        onClick={() => handleDeleteCell(stopTermMapId)}
                        size="small"
                        sx={{ position: 'absolute', top: 18, right: 2 }}
                      >
                        <DeleteIcon fontSize="small" color="secondary" sx={{ width: 15, height: 15 }} />
                      </IconButton>
                    </Grid>
                  )}

                  {(!isSavedCellStudent && isSelectedRow) ||
                  (selectedCell && !isSelectedEditRow) ||
                  (isSavedCellStudent && isSelectedEditRow) ? (
                    // <Stack
                    //   width={300}
                    //   px={1}
                    //   minHeight={60}
                    //   direction="row"
                    //   justifyContent="start"
                    //   // alignItems='center'
                    //   minWidth="100%"
                    //   gap={1}
                    // >
                    //   <div>
                    <Grid container minHeight={62} columnSpacing={1} px={1}>
                      <Grid
                        item
                        sx={{
                          flexBasis: '50%', // lg=6
                          transition: 'flex-basis 0.3s ease',
                          '.hoveredCell:hover &': !isSelectedRow
                            ? {
                                flexBasis: '45%',
                              }
                            : '',
                        }}
                        ml={0}
                      >
                        <Typography
                          fontSize={10}
                          color={isSelectedRow ? 'white' : isSelectedEditRow ? 'black' : 'white'}
                          variant="subtitle1"
                          m={0}
                        >
                          Pickup Point
                        </Typography>
                        <Autocomplete
                          clearIcon={false}
                          disableClearable
                          size="small"
                          options={pickupDropListDatas}
                          getOptionLabel={(option) => option.feeTitle || ''}
                          isOptionEqualToValue={(option, value) => option.feeId === value.feeId}
                          value={
                            pickupDropListDatas.find(
                              (opt) =>
                                opt.feeId ===
                                (individualPickupListFilter[`${row.studentId}_${item.termId}`] || pickupPointId)
                            ) || null
                          }
                          onChange={(_, newValue) => {
                            setShowErrorPickUp((prevMap) => ({
                              ...prevMap,
                              [`${row.studentId}_${item.termId}`]: false,
                            }));
                            setShowErrorDrop((prevMap) => ({ ...prevMap, [`${row.studentId}_${item.termId}`]: false }));
                            const feeId = newValue?.feeId || 0;
                            setIndividualPickupListFilter((prev) => ({
                              ...prev,
                              [`${row.studentId}_${item.termId}`]: feeId,
                            }));
                            setIndividualDropListFilter((prev) => ({
                              ...prev,
                              [`${row.studentId}_${item.termId}`]: feeId,
                            }));
                          }}
                          componentsProps={{
                            paper: {
                              sx: {
                                width: 250,
                              },
                            },
                          }}
                          error={showErrorPickUp[`${row.studentId}_${item.termId}`]}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              placeholder="Select"
                              size="small"
                              InputProps={{
                                ...params.InputProps,
                                sx: {
                                  height: 30,
                                  backgroundColor:
                                    isSelectedRow || selectedCell
                                      ? isLight
                                        ? theme.palette.common.white
                                        : theme.palette.common.black
                                      : isLight
                                      ? theme.palette.common.white
                                      : theme.palette.common.black,
                                  '& .MuiInputBase-input': {
                                    height: '20px',
                                    boxSizing: 'border-box',
                                  },
                                },
                              }}
                            />
                          )}
                          sx={
                            {
                              // width: 130,
                            }
                          }
                        />
                        {showErrorPickUp[`${row.studentId}_${item.termId}`] && (
                          <FormHelperText
                            sx={{
                              fontSize: 10,
                              position: 'absolute',
                              bottom: 0,
                              left: 60,
                              color: theme.palette.error.main,
                              fontWeight: 600,
                            }}
                          >
                            Required
                          </FormHelperText>
                        )}
                      </Grid>
                      <Grid
                        item
                        sx={{
                          flexBasis: '50%', // lg=6
                          transition: 'flex-basis 0.3s ease',
                          '.hoveredCell:hover &': !isSelectedRow
                            ? {
                                flexBasis: '45%', // lg=4
                              }
                            : '',
                        }}
                      >
                        <Typography
                          color={isSelectedRow ? 'white' : isSelectedEditRow ? 'black' : 'white'}
                          fontSize={10}
                          variant="subtitle1"
                        >
                          Drop Point
                        </Typography>
                        <Autocomplete
                          clearIcon={false}
                          disableClearable
                          size="small"
                          options={pickupDropListDatas}
                          getOptionLabel={(option) => option.feeTitle || ''}
                          isOptionEqualToValue={(option, value) => option.feeId === value.feeId}
                          value={
                            pickupDropListDatas.find(
                              (opt) =>
                                opt.feeId ===
                                (individualDropListFilter[`${row.studentId}_${item.termId}`] ??
                                  individualPickupListFilter[`${row.studentId}_${item.termId}`] ??
                                  dropPointId)
                            ) || null
                          }
                          onChange={(_, newValue) => {
                            setShowErrorDrop((prevMap) => ({ ...prevMap, [`${row.studentId}_${item.termId}`]: false }));
                            const feeId = newValue?.feeId || 0;
                            setIndividualDropListFilter((prev) => ({
                              ...prev,
                              [`${row.studentId}_${item.termId}`]: feeId,
                            }));
                          }}
                          componentsProps={{
                            paper: {
                              sx: {
                                width: 250,
                              },
                            },
                          }}
                          error={showErrorDrop[`${row.studentId}_${item.termId}`]}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              placeholder="Select"
                              size="small"
                              InputProps={{
                                ...params.InputProps,
                                sx: {
                                  height: 30,
                                  backgroundColor:
                                    isSelectedRow || selectedCell
                                      ? isLight
                                        ? theme.palette.common.white
                                        : theme.palette.common.black
                                      : isLight
                                      ? theme.palette.common.white
                                      : theme.palette.common.black,
                                  // transform: 'translateX(0px)',
                                  // transition: 'opacity 0.3s ease, transform 0.3s ease',
                                  // '.hoveredCell:hover &': !isSelectedRow
                                  //   ? {
                                  //       // width: 125,
                                  //       // transform: 'translateX(-17px)',
                                  //       pointerEvents: 'auto',
                                  //     }
                                  //   : '',
                                  '& .MuiInputBase-input': {
                                    height: '20px',
                                    // padding: '0 8px',
                                    boxSizing: 'border-box',
                                  },
                                },
                              }}
                            />
                          )}
                          sx={
                            {
                              // width: 130,
                            }
                          }
                        />
                        {showErrorDrop[`${row.studentId}_${item.termId}`] && (
                          <FormHelperText
                            sx={{
                              fontSize: 10,
                              position: 'absolute',
                              bottom: 0,
                              right: 60,
                              color: theme.palette.error.main,
                              fontWeight: 600,
                            }}
                          >
                            Required
                          </FormHelperText>
                        )}
                      </Grid>
                    </Grid>
                  ) : (
                    !isSavedCellStudent &&
                    !isSelectedRow && (
                      <Stack
                        onClick={() => {
                          if (!isSavedCellStudent) handleCellClick(row.studentId, item.termId);
                        }}
                        py={!isSavedCellStudent ? 2.2 : 0}
                        width="100%"
                        direction="row"
                        alignItems="center"
                        justifyContent="center"
                        position="relative"
                        minHeight={62}
                      >
                        {!isSavedCellStudent && isSelectedEditRow ? (
                          <ReportIcon
                            sx={{
                              color: theme.palette.grey[600],
                            }}
                          />
                        ) : (
                          <PersonAddIcon
                            sx={{
                              color: theme.palette.grey[600],
                            }}
                          />
                        )}
                      </Stack>
                    )
                  )}
                </Stack>
              </Button>
            );
          },
        }))
      : [];

    const baseColumns2: DataTableColumn<any>[] = [
      {
        name: 'Actions',
        renderHeader: () => (
          <Typography textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        renderCell: (row) => {
          const stopTermMapped = Array.isArray(row.stopTermMapped) ? row.stopTermMapped : [];
          const TermList = stopMappingListData.termList.map((m: TermListType) => m.termId);
          const TermIds = TermList.filter(
            (termId: number) => !stopTermMapped.some((f: StopTermMapped) => f.termId === termId)
          ).map((termId: number) => termId);

          const isSelectedRow = selectedRows.some(
            (selectedRow: StudentsMappedType) => selectedRow.studentId === row.studentId
          );
          const isSelectedEditRow = selectedEditRows.some(
            (selectedEditRow: StudentsMappedType) => selectedEditRow.studentId === row.studentId
          );
          return (
            <Stack width={100} direction="row" alignItems="center" justifyContent="center" gap={0.5}>
              {showSuccessIcon[row.studentId] === true ? (
                <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                  {succesResponse === 'Success' ? (
                    <>
                      <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                        Saved
                      </Typography>
                    </>
                  ) : succesResponse === 'Updated' ? (
                    <>
                      <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                        Updated
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.error.main} fontSize={7} variant="subtitle2">
                        Failed
                      </Typography>
                    </>
                  )}
                </Stack>
              ) : showError[row.studentId] === true ||
                showErrorPickUp[`${row.studentId}_${TermIds[0]}`] === true ||
                showErrorDrop[`${row.studentId}_${TermIds[0]}`] === true ? (
                <Stack direction="column" justifyContent="center" alignItems="center">
                  <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                  {(showError[row.studentId] ||
                    showErrorPickUp[`${row.studentId}_${TermIds[0]}`] ||
                    showErrorDrop[`${row.studentId}_${TermIds[0]}`]) && (
                    <Typography variant="subtitle2" fontSize={10} color="error">
                      {errorCountdownMap[`${row.studentId}_${TermIds[0]}`] || 5}{' '}
                    </Typography>
                  )}
                </Stack>
              ) : !individualSaveLoading[row.studentId] === true ? (
                <IconButton
                  disabled={!individualSaveButtonEnabled[row.studentId] && !(isSelectedEditRow || isSelectedRow)}
                  size="small"
                  color={isSelectedEditRow ? 'warning' : 'success'}
                  aria-label=""
                  onClick={() => handleSave(row)}
                >
                  <SaveIcon fontSize="small" />
                </IconButton>
              ) : (
                <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                  <>
                    <Lottie
                      animationData={checkedRows[row.studentId] ? UpdateLoading : SaveLoading}
                      loop
                      style={{ width: '20px' }}
                    />
                    <Typography
                      color={checkedRows[row.studentId] ? theme.palette.warning.main : theme.palette.success.main}
                      fontSize={7}
                      variant="subtitle2"
                    >
                      {checkedRows[row.studentId] ? 'Updating...' : 'Saving...'}
                    </Typography>
                  </>
                </Stack>
              )}

              <IconButton
                disabled={isSelectedRow || row.stopTermMapped === null}
                onClick={() => handleEditRowClick(row)}
                size="small"
                color="warning"
                aria-label="edit"
              >
                {isSelectedEditRow ? <CloseIcon2 fontSize="small" /> : <ModeEditIcon fontSize="small" />}
              </IconButton>
              <IconButton
                disabled={row.stopTermMapped === null}
                onClick={() => handleDeleteRow(row)}
                size="small"
                color="error"
                aria-label=""
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    theme,
    handleCellClick,
    selectedCells,
    stopMappingListData,
    individualSaveButtonEnabled,
    showError,
    checkedRows,
    handleSave,
    showSuccessIcon,
    individualSaveLoading,
    succesResponse,
    handleDeleteCell,
    handleDeleteRow,
    pickupDropListDatas,
    individualPickupListFilter,
    individualDropListFilter,
    selectedRows,
    busListDatas,
    busListFilter,
    errorCountdownMap,
    showErrorDrop,
    isLight,
    showErrorPickUp,
    allDropListFilter,
    allPickupListFilter,
    handleEditRowClick,
    selectedEditRows,
    handleRowAllClick,
    handleRowClick,
    isSelected,
    studentsData,
    studentNameSortOrder,
    admissionNoSortOrder,
    admNoToolbarSortLabel,
    studentNameToolbarSortLabel,
  ]);

  const getRowKey = useCallback((row: StudentsMappedType, index?: number) => {
    if (index === undefined) {
      return `${row.studentId}_undefined`;
    }
    return `${row.studentId}_${index}`;
  }, []);
  return (
    <Page title="Fees Collection">
      <OptionalFeeSetting2Root>
        <Box
          sx={{
            ...(isFullScreen && {
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh' ,
              // backgroundColor: 'white',
              zIndex: 9999,
              // p: 2,
            }),
            transition: 'all 0.3s ease-in-out',
          }}
        >
          <Card
            className="Card"
            elevation={1}
            sx={{ height: isFullScreen ? '100%' : ' calc(100vh - 160px)', px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}
          >
            <Stack direction="row" justifyContent="space-between">
              <Typography variant="h6" fontSize={17}>
                Bus Stop Settings
              </Typography>
              <Box pb={1} sx={{ flexShrink: 0 }}>
                {selectedRows.length > 0 && (
                  <Tooltip title="Delete">
                    <IconButton
                      sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                      aria-label="delete"
                      color="error"
                      onClick={handleDeleteMultiple}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title={isFullScreen ? 'Exit Full Screen' : 'Full Screen'}>
                  <IconButton size="small" onClick={toggleFullScreen} color="secondary" aria-label="fullscreen">
                    {isFullScreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                  </IconButton>
                </Tooltip>
                {showFilter === false ? (
                  <Tooltip title="Search">
                    <IconButton
                      aria-label="delete"
                      color="primary"
                      sx={{ mr: { xs: 0, sm: 1 } }}
                      onClick={() => setShowFilter((x) => !x)}
                    >
                      <SearchIcon />
                    </IconButton>
                  </Tooltip>
                ) : (
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <IoIosArrowUp />
                  </IconButton>
                )}
              </Box>
            </Stack>
            <Divider />
            <div className="card-main-body">
              <Collapse in={showFilter}>
                <form noValidate onReset={handleReset}>
                  <Grid pb={4} container spacing={2} alignItems="end">
                    <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Academic Year
                        </Typography>
                        <Select
                          labelId="academicYearFilter"
                          id="academicYearFilterSelect"
                          value={academicYearFilter?.toString()}
                          onChange={handleYearChange}
                          placeholder="Select Year"
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select Year
                          </MenuItem>
                          {YearData.map((opt) => (
                            <MenuItem key={opt.accademicId} value={opt.accademicId}>
                              {opt.accademicTime}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Type
                        </Typography>
                        <Select
                          labelId="feeTypeFilter"
                          id="feeTypeFilterSelect"
                          value={feeTypeFilter.toString()}
                          onChange={handleFeeTypeChange}
                          placeholder="Select Year"
                        >
                          <MenuItem sx={{ display: 'none' }} value={0}>
                            Select Type
                          </MenuItem>
                          {FEE_TYPE_ID_OPTIONS.map((opt) => (
                            <MenuItem disabled={opt.id === 1} key={opt.id} value={opt.id}>
                              {opt.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Section
                        </Typography>
                        <Select
                          labelId="classSectionsFilter"
                          id="classSectionsFilterSelect"
                          value={classSectionsFilter?.toString()}
                          onChange={handleClassSectionChange}
                          placeholder="Select Section"
                          MenuProps={{
                            sx: {
                              zIndex: 9999,
                            },
                            PaperProps: {
                              style: {
                                maxHeight: '270px', // Adjust the value to your desired height
                              },
                            },
                          }}
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select Section
                          </MenuItem>
                          {ClassSectionsData.map((opt) => (
                            <MenuItem key={opt.sectionId} value={opt.sectionId}>
                              {opt.sectionName}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Class
                        </Typography>
                        <Select
                          disabled={classListData.length === 0}
                          labelId="classFilter"
                          id="classFilter"
                          value={classFilter?.toString()}
                          onChange={handleClassChange}
                          placeholder="Select Class"
                          sx={{
                            '& .MuiInputBase-input.Mui-disabled': {
                              backgroundColor: theme.palette.grey[200],
                            },
                          }}
                          MenuProps={{
                            sx: {
                              zIndex: 9999,
                            },
                            PaperProps: {
                              style: {
                                maxHeight: '270px', // Adjust the value to your desired height
                              },
                            },
                          }}
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select Class
                          </MenuItem>
                          {classListData.map((opt) => (
                            <MenuItem key={opt.classId} value={opt.classId}>
                              {opt.className}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Student
                        </Typography>
                        <StudentsPickerField
                          width="100%"
                          // setSelectedStudentData={setSelectedStudentData}
                          componentsPropsWidth={350}
                          loadRecentPaidList={loadOptionalFeeList}
                          currentRecentPaidListRequest={currentOptionalFeeRequest}
                          setSelectedStudentIds={setSelectedStudentIds}
                          classId={classFilter}
                          academicId={academicYearFilter}
                          // multiple
                        />
                      </FormControl>
                    </Grid>
                    {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid> */}

                    <Grid item lg="auto">
                      <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                        {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                        <Button type="reset" variant="contained" color="secondary" fullWidth>
                          Reset
                        </Button>
                      </Stack>
                    </Grid>
                  </Grid>
                </form>
              </Collapse>
              {classFilter !== 0 && (
                <Box pr={1} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end" gap={1}>
                  <LoadingButton
                    loading={savingAll}
                    onClick={handleAllSave}
                    // disabled={saveAllButtonDisabled}
                    disabled={selectedCells.length === 0}
                    color="success"
                    startIcon={<SaveIcon fontSize="small" />}
                    size="small"
                    variant="contained"
                    sx={{ fontSize: 12 }}
                  >
                    {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                    {savingAll ? 'Saving All' : 'Save All'}
                  </LoadingButton>
                </Box>
              )}

              {classFilter !== 0 ? (
                <Paper
                  className="card-table-container"
                  sx={{
                    position: 'relative',
                    marginTop: '12px',
                    border: stopMappingListData.length !== 0 ? `2px solid ${theme.palette.grey[200]} ` : '',
                  }}
                >
                  {stopMappingSettingsStatus === 'loading' && (
                    <Stack alignItems="center" justifyContent="center" sx={{}}>
                      <Backdrop
                        open
                        sx={{
                          position: 'absolute',
                          backgroundColor: 'rgba(0, 0, 0, 0.2)', // Lighter gradient
                          zIndex: (theme) => theme.zIndex.drawer + 1,
                        }}
                      >
                        <CircularProgress />
                      </Backdrop>
                    </Stack>
                  )}

                  <DTVirtuoso
                    // disabledCheckBox={studentsData.map((m) => m.stopTermMapped?.length === 0)}
                    tableStyles={{ minWidth: { xs: '1100px' } }}
                    showHorizontalScroll
                    // ShowCheckBox
                    // disabledCheckBox={studentsData.map((m) => m.stopTermMapped?.length === 0)}
                    setSelectedRows={setSelectedRows}
                    selectedRows={selectedRows}
                    columns={OptionalFeeSettingListColumns}
                    // data={filteredData}
                    data={sortedData} // <-- sorted list here
                    getRowKey={getRowKey}
                    // fetchStatus={stopMappingSettingsStatus}
                    fetchStatus="success"
                  />
                </Paper>
              ) : (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="100%"
                  height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
                >
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Stack>
                </Box>
              )}
            </div>
          </Card>
        </Box>
      </OptionalFeeSetting2Root>
      <PositionedSnackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        content="Save All Successfully"
        open={snackBar}
        TransitionComponent="SlideTransition"
        autoHideDuration={2000}
        onClose={() => setSnackBar(false)}
      />
    </Page>
  );
}
