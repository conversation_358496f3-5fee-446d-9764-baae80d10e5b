import dayjs, { Dayjs } from 'dayjs';
import { FetchStatus } from './Common';

export type TermFeeRequest = {
  academicId: number;
};

// export type ClassSectionDataType = {};

export type ClassSectionType = {
  sectionId: number;
  sectionName: string;
};
export type FeeMappedType = {
  feeId: number;
  sectionId: number;
  amount: number;
  academicFeeId: number;
};
export type BasicFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  feeType: number;
  feeMapped: FeeMappedType[];
};
export type TermFeeDetailsDataType = {
  section: ClassSectionType;
  termFeeDetails: BasicFeeDetailsType[];
};
// export type TermFeeDetailsDataType = {
//   section: {
//     sectionId: number;
//     sectionName: string;
//   };

//   termFeeDetails: {
//     feeId: number;
//     feeTitle: string;
//     feeType: number;
//     feeMapped: {
//       feeId: number;
//       sectionId: number;
//       amount: number;
//       academicFeeId: number;
//     };
//   };
// };
// Class Sections
export type ClassSectionsDataType = {
  sectionId: string;
  sectionName: string;
};
// ===========================================================
// Fee Date Settings
export type FeeDateSettingsType = {
  terms: {
    termId: number;
    termTitle: string;
  };
  termFeeDetails: {
    feeId: number;
    feeTitle: string;
    feeType: number;
    termFeeMapped: {
      termId: number;
      amount: number;
      academicTermId: number;
    };
  };
};

export type TermFeeDataType = {
  termId: number;
  termTitle: string;
};
export type TermFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  feeType: number;
  termFeeMapped: {
    termId: number;
    amount: number;
    academicTermId: number;
  };
};
export type TermFeeMappedDataType = {
  termId: number;
  amount: number;
};
export type AllFeeDateSettingsType = TermFeeDataType & TermFeeDetailsType & TermFeeMappedDataType;

// ===========================================================================

// Term Fee Settings
export type TermFeeSettingsDataType = {
  terms: [
    {
      termId: number;
      termTitle: string;
    }
  ];
  termFeeDetails: [
    {
      feeId: number;
      feeTitle: string;
      amount: number;
      feeType: number;
      termFeeMapped: [
        {
          termId: number;
          amount: number;
        }
      ];
    }
  ];
};

// ===========================================================================
// Get Class
export type ClassListDataType = {
  classId: string;
  className: string;
};

// ===========================================================================
// Student Fee Status
export type StudentFeeStatusDataType = {
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  admissionNo: string;
  studentName: string;
  className: string;
  totalFee: number;
  paid: number;
  balance: number;
  image: string;
  gender: string;
  guardianName: string;
  guardianNumber: string;
  contactNo: string;
};

// ===========================================================================
// student Term Fee Status List
export type StudentTermFeeStatusDataType = {
  terms: [
    {
      termId: number;
      termTitle: string;
    }
  ];
  termFeeDetails: [
    {
      feeId: number;
      feeTitle: string;
      totalFee: number;
      paid: number;
      balance: number;
      termFeeStats: [
        {
          termId: number;
          amount: number;
          scholarship: number;
          discount: number;
          fine: number;
          paid: number;
          balance: number;
        }
      ];
    }
  ];
  paymentTypes: [
    {
      paymentTypeId: number;
      paymentType: string;
    }
  ];
  bankDetails: [
    {
      bankId: number;
      bankName: string;
    }
  ];
  receiptType: {
    receiptType: string;
  };
};

export type StudentTermFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  feeAmount: number;
  feePaid: number;
  feeBalance: number;
  termId: number;
  termTitle: string;
  termAmount: number;
  scholarship: number;
  discount: number;
  fine: number;
  termPaid: number;
  termBalance: number;
};

export type StudentTermFeeStatusNewType = {
  termFeeDetails: StudentTermFeeDetailsType[];
  paymentTypes: [
    {
      paymentTypeId: number;
      paymentType: string;
    }
  ];
  bankDetails: [
    {
      bankId: number;
      bankName: string;
    }
  ];
  receiptPrintType: {
    receiptType: string;
  };
};

// StudentTermFeePayType
export type StudentTermFeePayType = {
  adminId: number;
  academicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  totalAmount: number;
  totalScholarship: number;
  totalDiscount: number;
  totalFine: number;
  grandTotal: number;
  remarks: string;
  payingDate: string;
  payingReceiptNo: number;
  feeTypeFilter: number;
  payStatus: string;
  receiptNo: number;
  grandTotalWords?: string;
  termfeepaying: {
    feeId: number;
    termId: number;
    totalAmount: number;
    scholarship: number;
    discount: number;
    fine: number;
    grandTotal: number;
    payStatus: string;
    receiptDetailsId: number;
  }[];
  paymentmode: {
    paymentTypeId: number;
    amount: number;
    chequeOrDDNo: string;
    dateOfIssue: string;
    deppositDate: string;
    clearingDate: string;
    bankId: number;
    payStatus: string;
  }[];
  receiptPrintType: {
    receiptType: string;
  };
};

// ==============================================
// Create Basic Fee Setting
export type CreateBasicFeeSettingDataType = {
  adminId: number;
  accademicId: number;
  feeId: number;
  feeType: number;
  feeTypeId: number;
  feeAmount: {
    sectionId: number;
    amount: number;
    dbResult: string;
    academicFeeId: number;
  }[];
};

// ==============================================
// Create Term Fee Setting
export type CreateTermFeeSettingDataType = {
  adminId: number;
  accademicId: number;
  academicFeeId: number;
  sectionId: number;
  feeId: number;
  feeTypeId: number;
  termAmount: {
    termId: number;
    amount: number;
    dbResult: string;
    academicTermId: number;
  }[];
}[];
// ==============================================
// Create Basic Fee Setting Title
export type CreateBasicFeeSettingTitleDataType = {
  adminId: number;
  feeId: any;
  feeTitle: string;
  feeType: number | string;
  status?: number;
  startDate: Dayjs | string | number | null;
  endDate: Dayjs | string | number | null;
  feeTypeId: number | string;
};

// FEE_TYPE_OPTIONS
export type FeeTypeOptions = {
  id: number;
  name: string;
};
// Create Term Fee Setting Title
export type CreateTermFeeSettingTitleDataType = {
  adminId: number;
  termId: number;
  termTitle: string;
  status?: number;
  startDate: Dayjs | string | null;
  endDate: Dayjs | string | null;
  feeTypeId: number;
};
// ==============================================

// ==============================================
// Get Optional Fee Settings //
export type GetOptionalFeeSettingsDataType = {
  optionalFee: {
    feeId: number;
    feeTitle: string;
  };
  studentsMapped: {
    studentId: number;
    admissionNo: string;
    studentName: string;
    feeMapped: {
      feeId: number;
      studentId: number;
    };
  };
};
export type FeeMappedDataType = {
  [x: string]: any;
  // find(arg0: (i: { feeId: number; studentId: any }) => boolean): unknown;
  // map(arg0: (i: GetOptionalFeeStudentMapDataType) => number): unknown;
  optionalMapId: number;
  feeId: number;
  studentId: number;
};
//  Optional Fee Student Mapped Data
export type GetOptionalFeeStudentMapDataType = {
  studentId: number;
  admissionNo: string;
  studentName: string;
  feeMapped: FeeMappedDataType;
};
// ==============================================
// Get Optional Fee Settings //
export type OptionalFeeSettingsDataType = {
  adminId: number;
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeTypeId: number;
  optionalFee: {
    feeId: number;
    dbResult: string;
    optionalMapId: number;
  }[];
}[];
// ==============================================

// ==============================================
// Get Individual Fee Settings //
export type IndividualFeeMappedDataType = {
  optionalMapId: number;
  feeId: number;
  studentId: number;
  amount: number;
};

export type GetIndividualFeeStudentMapDataType = {
  studentId: number;
  admissionNo: string;
  studentName: string;
  feeMapped: IndividualFeeMappedDataType[];
};

export type GetIndividualFeeSettingsDataType = {
  optionalFee: {
    feeId: number;
    feeTitle: string;
  }[];
  studentsMapped: GetIndividualFeeStudentMapDataType[];
};

// Individual Fee Settings Request Type
export type IndividualFeeSettingsDataType = {
  adminId: number;
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeTypeId: number;
  optionalFee: {
    feeId: number;
    amount: number;
    dbResult: string;
    optionalMapId: number;
  }[];
}[];
// ==============================================

// Create Scolarship Fee Setting
export type CreateScholarshipFeeSettingDataType = [
  {
    accademicId: number;
    title: string;
    type: number;
    description: string;
    adminId: number;
    dbResult?: string;
    scholarshipId: number;
    feeTypeId?: number;
    scholarshipValues: {
      feeId: number;
      termId: number;
      type: number;
      value: number;
      dbResult: string;
      scholarshipMapId: number;
    }[];
  }
];

export type ScholarshipFeeDetailsType = {
  feeId: number;
  feeTitle: string;
};

export type ScholarshipTermDetailsType = {
  termId: number;
  termTitle: string;
};

export type ScholarshipDetailsType = {
  id?: number;
  scholarshipId: number;
  title: string;
  type: number;
  description: string;
  status: number;
  dbResult?: string;
  scholarshipMapped: {
    scholarshipMapId: number;
    feeId: number;
    termId: number;
    type: number;
    value: number;
  };
};

// Extending all three types
export type ScholarshipFeeListType = ScholarshipFeeDetailsType & ScholarshipTermDetailsType & ScholarshipDetailsType;

// ----- ------ ------ ------
// Get Scholarship Settings //
export type GetScholarshipSettingsDataType = {
  scholarship: {
    scholarshipId: number;
    title: string;
  };
  studentsMapped: {
    studentId: number;
    admissionNo: string;
    studentName: string;
    scholarshipMapped: {
      scholarshipStudentMapId: number;
      scholarshipId: number;
      studentId: number;
    };
  };
};
export type GetScholarshipDataType = {
  scholarshipId: number;
  title: string;
};
export type GetStudentsMappedDataType = {
  [x: string]: any;
  studentId: number;
  admissionNo: string;
  studentName: string;
  scholarshipMapped: {
    [x: string]: any;
    find: any;
    scholarshipStudentMapId: number;
    scholarshipId: number;
    studentId: number;
  };
};
// Scholarship Settings //
export type CreateScholarshipSettingsDataType = {
  adminId: number;
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeTypeId: number;
  scholarshipMap: {
    scholarshipId: number;
    dbResult: string;
    scholarshipStudentMapId: number;
  };
};
// Scholarship Settings //
export type DeleteScholarshipType = {
  adminId: number;
  accademicId: number;
  scholarshipId: number;
  dbResult: string;
  deletedId: number;
};
// ================== Term Fee crud ============================
// Get Term Fee List
export type GetTermFeeListType = {
  adminId: number;
  accademicId: number;
  termTitle: string;
  feeTypeId: number;
};
// Delete Term Fee List
export type DeleteTermFeeListType = {
  adminId: number;
  accademicId: number;
  termId: number;
  dbResult: string;
  deletedId: number;
};
// Update Term Fee List
export type UpdateTermFeeListType = {
  adminId: number;
  termId: number;
  termTitle: string;
  startDate: string | dayjs.Dayjs | null;
  endDate: string | dayjs.Dayjs | null;
  status?: number;
  dbResult?: string;
  updatedId?: number;
};
// ==============================================
// ================== Basic Fee crud ============================
export type GetBasicFeeListRequestType = {
  adminId: number;
  accademicId: number | string;
  feeTitle: string;
  feeTypeId: number;
};
export type DeleteBasicFeeListType = {
  adminId: number;
  accademicId: number | string;
  feeId: number;
  dbResult: string;
  deletedId: number;
};
export type UpdateBasicFeeListType = {
  adminId: number;
  feeId: number;
  feeTitle: string;
  feeType: number;
  startDate: Dayjs | string | number | null;
  endDate: Dayjs | string | number | null;
  status: number;
  dbResult: string;
  updatedId: number;
};
// ==============================================

// ============== Manage Fee OverView ===========
export type GetFeeOverviewStatusType = {
  thisYearTotalFee: number;
  thisYearTotalPaid: number;
  thisYearTotalBalance: number;
  prevoiusMonthCollection: number;
  thisMonthCollection: number;
  todaysCollection: number;
};
export type GetFeeOverviewChartType = {
  monthName: string;
  totalFeeCollected: string;
};
export type FeeOverviewPaidListRequest = {
  adminId: number | undefined;
  academicId: number;
  feeTypeId: number;
  classId: number;
  studentId: string;
  paidDate: string;
};
export type GetFeeOverviewPaidListType = {
  receiptId: number;
  studentId: number;
  studentName: string;
  className: string;
  grandTotal: number;
  paidDate: string;
  status: string;
  billdeskOrderNumber: string;
  billdeskTransactionId: string;
};
export type GetFeeOverviewModeListType = {
  paymentTypeId: number;
  paymentType: string;
  paidList: {
    receiptId: number;
    studentId: number;
    studentName: string;
    className: string;
    createdDate: string;
    grandTotal: number;
    status: string;
  };
};
// ==============================================
// Get Receipt For Print
// export type GetReceiptForPrintType = {
export type GetReceiptDetailsType = {
  receiptId: number;
  createdDate: string;
  schoolName: string;
  schoolAddress: string;
  schoolPhone: string;
  schoolEmail: string;
  schoolLogo: string;
  studentId: number;
  studentName: string;
  studentImage: string;
  className: string;
  totalAmount: number;
  scholarshipAmount: number;
  discountAmount: number;
  fineAmount: number;
  grandTotal: number;
  remarks: string;
  receiptType: string;
  contactNo: string;
  admissionNo: string;
  grandTotalWords: string;
  receiptNo: number;
};
export type GetReceiptTermfeepaidType = {
  receiptDetailsId: number;
  receiptId: number;
  feeId: number;
  feeTitle: string;
  termId: number;
  termTitle: string;
  totalAmount: number;
  scholarshipAmount: number;
  discountAmount: number;
  fineAmount: number;
  grandTotal: number;
};
export type GetReceiptPaymentModeType = {
  paymentModeId: number;
  paymentTypeId: number;
  receiptId: number;
  paymentType: string;
  amount: number;
  chequeOrDDNo: string;
  dateOfIssue: string;
  deppositDate: string;
  clearingDate: string;
  bankId: number;
  bankName: string;
};

export type GetReceiptForPrintType = {
  receiptDetail: GetReceiptDetailsType;
  termfeepaid: GetReceiptTermfeepaidType[];
  paymentmode: GetReceiptPaymentModeType[];
};

// ==============================================
export type GetFeePaidListType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  fromDate: string;
  toDate: string;
};
export type GetFeePaidListDataType = {
  receiptId: number;
  academicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  studentName: string;
  admissionNo: number;
  className: string;
  paidDate: string;
  paidAmount: number;
  remarks: string;
  totalPaid: number;
  paymentType: string;
  chequeNo: string;
  billdeskOrderNumber: string;
  billdeskTransactionId: string;
};

export type GetStudentsFilterDataType = {
  studentId: string;
  studentName: string;
  admissionNo: string;
  className: string;
};
// ==============================================
export type GetFeePaidBasicListRequestType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
  fromDate: string;
  toDate: string;
};
export type GetFeePaidBasicListDataType = {
  receiptId: number;
  academicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
  feeTitle: string;
  studentName: string;
  admissionNo: number;
  className: string;
  paidDate: string;
  paidAmount: number;
  remarks: string;
  totalPaid: number;
};

export type GetBasicFeeFilterDataType = {
  feeId: string;
  feeTitle: string;
};
// ==============================================
export type GetFeePaidTermListRequestType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
  termId: number;
  fromDate: string;
  toDate: string;
};
export type GetFeePaidTermListDataType = {
  receiptId: number;
  academicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
  feeTitle: string;
  termId: number;
  termTitle: string;
  studentName: string;
  admissionNo: number;
  className: string;
  paidDate: string;
  paidAmount: number;
  remarks: string;
  totalPaid: number;
};

export type GetTermFeeFilterDataType = {
  termId: string;
  termTitle: string;
};
// ==============================================
// Get Fee Pending List
export type GetFeePendingListRequestType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
};
export type GetFeePendingListDataType = {
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  admissionNo: string;
  studentName: string;
  className: string;
  totalFee: number;
  paid: number;
  balance: number;
  image: string;
  gender: string;
  guardianName: string;
  guardianNumber: string;
};
// Get Fee Pending Basic List
export type GetFeePendingBasicListRequestType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
};
export type GetFeePendingBasicListDataType = {
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  admissionNo: string;
  studentName: string;
  className: string;
  feeId: number;
  feeTitle: string;
  totalFee: number;
  paid: number;
  balance: number;
  image: string;
  gender: string;
  guardianName: string;
  guardianNumber: string;
};
// Get Fee Pending Term List
export type GetFeePendingTermListRequestType = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeId: number;
  termId: number;
};
export type GetFeePendingTermListDataType = {
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  admissionNo: string;
  studentName: string;
  className: string;
  feeId: number;
  feeTitle: string;
  termId: number;
  termTitle: string;
  totalFee: number;
  paid: number;
  balance: number;
  image: string;
  gender: string;
  guardianName: string;
  guardianNumber: string;
};

// Get Fine List
export type GetFineListRequestType = {
  adminId: number;
  academicId: number;
  title: string;
  feeTypeId: number;
};
export type GetFineListDataType = {
  id?: number;
  fineId: number;
  title: string;
  type: number;
  value: number;
  growAmount: number;
  incrementDays: number;
  status: number;
  feeTypeId: number;
};
// Create and  Edit Fine List
export type CreateEditFineListRequestType = {
  accademicId: number;
  adminId: number;
  fineId: number;
  title: string;
  type: number;
  value: number;
  growAmount: number;
  incrementDays: number;
  status: number;
  dbResult: string;
  resultId: number;
  feeTypeId: number;
}[];
export type CreateEditFineListResponseType = {
  adminId: number;
  termId: number;
  termTitle: string;
  startDate: string;
  endDate: string;
  status: number;
  dbResult: string;
  updatedId: number;
};
// Delete Fine List
export type DeleteFineListRequestType = {
  adminId: number;
  accademicId: number;
  fineId: number;
  dbResult: string;
  deletedId: number;
}[];
export type DeleteFineListResponseType = {
  adminId: number;
  accademicId: number;
  fineId: number;
  dbResult: string;
  deletedId: number;
};
// Get Fine Mapping List
export type GetFineMappingListDataType = {
  feeList: [
    {
      feeId: number;
      feeTitle: string;
    }
  ];
  termList: [
    {
      termId: number;
      termTitle: string;
    }
  ];
  fineTypeList: [
    {
      fineId: number;
      title: string;
    }
  ];
  fineMappedList: [
    {
      feeId: number;
      fineMappedTerms: [
        {
          fineMappedId: number;
          termId: number;
          fineId: number;
        }
      ];
    }
  ];
};
// ==============================================
// Fine Map Insert
export type FineMapInsertDataType = {
  adminId: number;
  accademicId: number;
  feeId: number;
  dbResult: string;
  fineMappedValues: {
    fineMappedId: number;
    termId: number;
    fineId: number;
    dbResult: string;
    dbResultId: number;
  }[];
}[];
// Delete Fine Map
export type FineMapDeleteDataType = {
  adminId: number;
  accademicId: number;
  fineMappedId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Delete Basic Fee Mapped
export type BasicFeeMappedDeleteDataType = {
  adminId: number;
  accademicId: number;
  academicFeeId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Delete All Basic Fee Mapped
export type BasicFeeMappedDeleteAllDataType = {
  adminId: number;
  accademicId: number;
  feeId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Delete Term Fee Mapped
export type TermFeeMappedDeleteDataType = {
  adminId: number;
  accademicId: number;
  academicTermId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Delete All Term Fee Mapped
export type TermFeeMappedDeleteAllDataType = {
  adminId: number;
  accademicId: number;
  feeId: number;
  sectionId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Receipt Cancel
export type ReceiptCancelRequestType = {
  adminId: number | undefined;
  receiptNo: number | undefined;
  reason: string;
  deletedId: number;
  dbResult: string;
};
export type CheckReceiptNoType = {
  id: number;
  result: string;
};
// Receipt Approve
export type ReceiptApproveRequestType = {
  adminId: number | undefined;
  receiptNo: number | undefined;
  reason: string;
  transactionId: string;
  aprovedId: number;
  dbResult: string;
};
// ==============================================
// Delete Optional Fee Mapped
export type OptionalFeeMappedDeleteType = {
  adminId: number;
  accademicId: number;
  optionalMapId: number;
  dbResult: string;
  deletedId: number;
};
// Delete All Optional Fee Mapped
export type OptionalFeeMappedDeleteAllType = {
  adminId: number;
  accademicId: number;
  classId: number;
  studentId: number | undefined;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Delete Scholarship Mapped
export type ScholarshipMappedDeleteType = {
  adminId: number;
  accademicId: number;
  scholarshipStudentMapId: number;
  dbResult: string;
  deletedId: number;
};
// Delete All Scholarship Mapped
export type ScholarshipMappedDeleteAllType = {
  adminId: number;
  accademicId: number;
  classId: number;
  studentId: number;
  dbResult: string;
  deletedId: number;
};
// ==============================================
// Get Stop Mapping Settings
export type PickupDropListType = {
  feeId: number;
  feeTitle: string;
};
export type TermListType = {
  termId: number;
  termTitle: string;
};
export type GetStopTermMappedType = {
  stopTermMapId: number;
  studentId: number;
  feeId: number;
  termId: number;
  pickupId: number;
  dropId: number;
};
export type StudentsMappedType = {
  studentId: number;
  admissionNo: string;
  studentName: string;
  stopTermMapped: GetStopTermMappedType[];
};

export type BusListType = {
  busId: number;
  busName: string;
};
export type StopMappingSettingsType = {
  pickupDropList: PickupDropListType[];
  termList: TermListType[];
  studentsMapped: StudentsMappedType[];
  busList: BusListType[];
};

// Create Stop Mapping
export type StopTermMapped = {
  feeId: number;
  termId: number;
  pickupId: number;
  dropId: number;
  dbResult: string;
  stopTermMapId: number;
};
export type CreateStopMappingType = {
  adminId: number;
  accademicId: number;
  sectionId: number;
  classId: number;
  studentId: number;
  feeTypeId: number;
  busId: number;
  stopTermMapped: StopTermMapped[];
};

// Delete Bus Mapped
export type DeleteBusMappedType = {
  adminId: number;
  accademicId: number;
  stopTermMapId: number;
  dbResult: string;
  deletedId: number;
};
// Delete All Bus Mapped Student

export type DeleteAllBusMappedStudentType = {
  adminId: number;
  accademicId: number;
  classId: number;
  studentId: number;
  dbResult: string;
  deletedId: number;
};

// Student Picker
export type StudentPickerRequest = {
  academicId: number | undefined;
  adminId: number;
  classId: number;
  search: string;
};
export type GetStudentPickerDataType = {
  studentId: number;
  image: string;
  studentName: string;
  gender: string;
  admissionNo: string;
  className: string;
  fatherName: string;
  fatherNumber: string;
  motherName: string;
  motherNumber: string;
};

// Daily Fee Payment List
export type TakeFeePaidDailyListRequest = {
  adminId: number;
  academicId: number;
  feeTypeId: number;
  classId: number;
  termId: number;
  fromDate: string;
  toDate: string;
};

export type TakeFeePaidDailyListResponse = {
  feeId: number;
  feeTitle: string;
  totalPaid: number;
};

// Student Picker New Version
export type StudentPickerNewRequest = {
  adminId: number;
  academicId: number;
  sectionId: number;
  classId: number;
  search: string;
};

export type StudentPickerNewResponse = {
  studentId: number;
  image: string;
  studentName: string;
  gender: string;
  admissionNo: string;
  className: string;
  fatherName: string;
  fatherNumber: string;
  motherName: string;
  motherNumber: string;
};

// ==============================================
// Manage Fee State //
export type ManageFeeState = {
  feeSettingsList: {
    status: FetchStatus;
    data: TermFeeDetailsDataType[];
    error: string | null;
  };
  classSectionsList: {
    status: FetchStatus;
    data: ClassSectionsDataType[];
    error: string | null;
  };
  feeDateSettingsList: {
    status: FetchStatus;
    data: FeeDateSettingsType[];
    error: string | null;
  };
  termFeeSettingsList: {
    status: FetchStatus;
    data: TermFeeSettingsDataType[];
    error: string | null;
  };
  classList: {
    status: FetchStatus;
    data: ClassListDataType[];
    error: string | null;
  };
  studentsFeeStatusList: {
    status: FetchStatus;
    data: StudentFeeStatusDataType[];
    error: string | null;
  };
  studentTermFeeStatusList: {
    status: FetchStatus;
    data: StudentTermFeeStatusDataType[];
    error: string | null;
  };
  studentTermFeeStatusNewList: {
    status: FetchStatus;
    data: StudentTermFeeStatusNewType | null;
    error: string | null;
  };
  studentTermFeePayDatas: {
    status: FetchStatus;
    data: StudentTermFeePayType[];
    error: string | null;
  };
  createBasicFeeSettingList: {
    status: FetchStatus;
    data: CreateBasicFeeSettingDataType[];
    error: string | null;
  };
  createTermFeeSettingList: {
    status: FetchStatus;
    data: CreateTermFeeSettingDataType[];
    error: string | null;
  };
  createBasicFeeSettingTitleList: {
    status: FetchStatus;
    data: CreateBasicFeeSettingTitleDataType[];
    error: string | null;
  };
  createTermFeeSettingTitleList: {
    status: FetchStatus;
    data: CreateTermFeeSettingTitleDataType[];
    error: string | null;
  };
  optionalFeeSettingsList: {
    status: FetchStatus;
    data: GetOptionalFeeSettingsDataType[];
    error: string | null;
  };
  individualFeeSettingsList: {
    status: FetchStatus;
    data: GetIndividualFeeSettingsDataType | null;
    error: string | null;
  };
  createOptionalFeeSettingsList: {
    status: FetchStatus;
    data: OptionalFeeSettingsDataType[];
    error: string | null;
  };
  createIndividualFeeSettingsList: {
    status: FetchStatus;
    data: IndividualFeeSettingsDataType[];
    error: string | null;
  };
  ScholarshipFeeList: {
    status: FetchStatus;
    data: ScholarshipFeeListType | null;
    error: string | null;
  };
  createScholarshipFeeSettingList: {
    status: FetchStatus;
    data: CreateScholarshipFeeSettingDataType | null;
    error: string | null;
  };
  scholarshipSettingsList: {
    status: FetchStatus;
    data: GetScholarshipSettingsDataType[];
    error: string | null;
  };
  createScholarshipSettingsList: {
    status: FetchStatus;
    data: CreateScholarshipSettingsDataType[];
    error: string | null;
  };
  termFeeList: {
    status: FetchStatus;
    data: CreateTermFeeSettingTitleDataType[];
    error: string | null;
  };
  basicFeeList: {
    status: FetchStatus;
    data: CreateBasicFeeSettingTitleDataType[];
    error: string | null;
  };

  feeOverviewStatusList: {
    status: FetchStatus;
    data: GetFeeOverviewStatusType | null;
    error: string | null;
  };
  feeOverviewChartList: {
    status: FetchStatus;
    data: GetFeeOverviewChartType[];
    error: string | null;
  };
  feeOverviewPaidList: {
    status: FetchStatus;
    data: GetFeeOverviewPaidListType[];
    error: string | null;
  };
  feeOverviewModeList: {
    status: FetchStatus;
    data: GetFeeOverviewModeListType[];
    error: string | null;
  };
  receiptForPrintList: {
    status: FetchStatus;
    data: GetReceiptForPrintType[];
    error: string | null;
  };
  feePaidList: {
    status: FetchStatus;
    data: GetFeePaidListDataType[];
    error: string | null;
  };
  studentsFilter: {
    status: FetchStatus;
    data: GetStudentsFilterDataType[];
    error: string | null;
  };
  feePaidBasicList: {
    status: FetchStatus;
    data: GetFeePaidBasicListDataType[];
    error: string | null;
  };
  basicFeeFilter: {
    status: FetchStatus;
    data: GetBasicFeeFilterDataType[];
    error: string | null;
  };
  feePaidTermList: {
    status: FetchStatus;
    data: GetFeePaidTermListDataType[];
    error: string | null;
  };
  termFeeFilter: {
    status: FetchStatus;
    data: GetTermFeeFilterDataType[];
    error: string | null;
  };
  feePendingList: {
    status: FetchStatus;
    data: GetFeePendingListDataType[];
    error: string | null;
  };
  feePendingBasicList: {
    status: FetchStatus;
    data: GetFeePendingBasicListDataType[];
    error: string | null;
  };
  feePendingTermList: {
    status: FetchStatus;
    data: GetFeePendingTermListDataType[];
    error: string | null;
  };
  fineList: {
    status: FetchStatus;
    data: GetFineListDataType[];
    error: string | null;
  };
  fineMappingList: {
    status: FetchStatus;
    data: GetFineMappingListDataType[];
    error: string | null;
  };
  fineMapList: {
    status: FetchStatus;
    data: FineMapInsertDataType[];
    error: string | null;
  };
  checkReceiptNo: {
    status: FetchStatus;
    data: CheckReceiptNoType | null;
    error: string | null;
  };
  stopMappingSettingsList: {
    status: FetchStatus;
    data: StopMappingSettingsType[];
    error: string | null;
  };
  studentPickerList: {
    status: FetchStatus;
    data: GetStudentPickerDataType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
