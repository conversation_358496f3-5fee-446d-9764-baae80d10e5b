import { useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

export interface FeeRouteHistoryOptions {
  onRouteChange?: (path: string) => void;
  preserveScrollPosition?: boolean;
  enableBackNavigation?: boolean;
}

/**
 * Custom hook to manage browser history for fee management routes
 * Provides utilities for proper navigation behavior and history management
 */
export function useFeeRouteHistory(options: FeeRouteHistoryOptions = {}) {
  const navigate = useNavigate();
  const location = useLocation();
  const { onRouteChange, preserveScrollPosition = true, enableBackNavigation = true } = options;

  // Handle route changes
  useEffect(() => {
    if (onRouteChange) {
      onRouteChange(location.pathname);
    }
  }, [location.pathname, onRouteChange]);

  // Preserve scroll position on route changes
  useEffect(() => {
    if (preserveScrollPosition) {
      // Save scroll position before route change
      const saveScrollPosition = () => {
        sessionStorage.setItem(
          `scroll-${location.pathname}`,
          JSON.stringify({
            x: window.scrollX,
            y: window.scrollY,
          })
        );
      };

      // Restore scroll position after route change
      const restoreScrollPosition = () => {
        const savedPosition = sessionStorage.getItem(`scroll-${location.pathname}`);
        if (savedPosition) {
          try {
            const { x, y } = JSON.parse(savedPosition);
            window.scrollTo(x, y);
          } catch (error) {
            console.warn('Failed to restore scroll position:', error);
          }
        }
      };

      // Save position before leaving
      window.addEventListener('beforeunload', saveScrollPosition);
      
      // Restore position on mount
      const timeoutId = setTimeout(restoreScrollPosition, 100);

      return () => {
        window.removeEventListener('beforeunload', saveScrollPosition);
        clearTimeout(timeoutId);
      };
    }
  }, [location.pathname, preserveScrollPosition]);

  // Navigation utilities
  const navigateWithHistory = useCallback(
    (path: string, options: { replace?: boolean; state?: any } = {}) => {
      const { replace = false, state } = options;
      
      // Store current path in history for back navigation
      if (!replace && enableBackNavigation) {
        const currentHistory = JSON.parse(
          sessionStorage.getItem('fee-route-history') || '[]'
        );
        currentHistory.push(location.pathname);
        // Keep only last 10 entries to prevent memory issues
        if (currentHistory.length > 10) {
          currentHistory.shift();
        }
        sessionStorage.setItem('fee-route-history', JSON.stringify(currentHistory));
      }

      navigate(path, { replace, state });
    },
    [navigate, location.pathname, enableBackNavigation]
  );

  const goBack = useCallback(() => {
    if (enableBackNavigation) {
      const history = JSON.parse(
        sessionStorage.getItem('fee-route-history') || '[]'
      );
      
      if (history.length > 0) {
        const previousPath = history.pop();
        sessionStorage.setItem('fee-route-history', JSON.stringify(history));
        navigate(previousPath, { replace: true });
        return true;
      }
    }
    
    // Fallback to browser back if no custom history
    if (window.history.length > 1) {
      window.history.back();
      return true;
    }
    
    return false;
  }, [navigate, enableBackNavigation]);

  const clearHistory = useCallback(() => {
    sessionStorage.removeItem('fee-route-history');
  }, []);

  // Check if we can go back
  const canGoBack = useCallback(() => {
    if (enableBackNavigation) {
      const history = JSON.parse(
        sessionStorage.getItem('fee-route-history') || '[]'
      );
      return history.length > 0 || window.history.length > 1;
    }
    return window.history.length > 1;
  }, [enableBackNavigation]);

  return {
    navigateWithHistory,
    goBack,
    clearHistory,
    canGoBack,
    currentPath: location.pathname,
    locationState: location.state,
  };
}

export default useFeeRouteHistory;
