import { useCallback, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

export interface FeeRouteStateOptions {
  stateKey: string;
  persistAcrossRoutes?: boolean;
  clearOnUnmount?: boolean;
}

export interface FeeRouteState<T = any> {
  data: T;
  timestamp: number;
  route: string;
}

/**
 * Custom hook to manage state across fee route transitions
 * Preserves form data and component state when navigating between routes
 */
export function useFeeRouteState<T = any>(
  initialState: T,
  options: FeeRouteStateOptions
) {
  const location = useLocation();
  const { stateKey, persistAcrossRoutes = true, clearOnUnmount = false } = options;
  const stateRef = useRef<T>(initialState);
  const storageKey = `fee-route-state-${stateKey}`;

  // Load state from storage on mount
  useEffect(() => {
    if (persistAcrossRoutes) {
      try {
        const storedState = sessionStorage.getItem(storageKey);
        if (storedState) {
          const parsedState: FeeRouteState<T> = JSON.parse(storedState);
          // Check if state is still valid (not too old)
          const isValid = Date.now() - parsedState.timestamp < 30 * 60 * 1000; // 30 minutes
          if (isValid) {
            stateRef.current = parsedState.data;
          } else {
            sessionStorage.removeItem(storageKey);
          }
        }
      } catch (error) {
        console.warn('Failed to load route state:', error);
        sessionStorage.removeItem(storageKey);
      }
    }
  }, [storageKey, persistAcrossRoutes]);

  // Save state to storage when it changes
  const saveState = useCallback(
    (newState: T) => {
      stateRef.current = newState;
      
      if (persistAcrossRoutes) {
        try {
          const stateToSave: FeeRouteState<T> = {
            data: newState,
            timestamp: Date.now(),
            route: location.pathname,
          };
          sessionStorage.setItem(storageKey, JSON.stringify(stateToSave));
        } catch (error) {
          console.warn('Failed to save route state:', error);
        }
      }
    },
    [storageKey, location.pathname, persistAcrossRoutes]
  );

  // Update state
  const setState = useCallback(
    (newState: T | ((prevState: T) => T)) => {
      const updatedState = typeof newState === 'function' 
        ? (newState as (prevState: T) => T)(stateRef.current)
        : newState;
      saveState(updatedState);
    },
    [saveState]
  );

  // Clear state
  const clearState = useCallback(() => {
    stateRef.current = initialState;
    if (persistAcrossRoutes) {
      sessionStorage.removeItem(storageKey);
    }
  }, [initialState, storageKey, persistAcrossRoutes]);

  // Get current state
  const getState = useCallback(() => stateRef.current, []);

  // Check if state exists
  const hasState = useCallback(() => {
    if (persistAcrossRoutes) {
      return sessionStorage.getItem(storageKey) !== null;
    }
    return stateRef.current !== initialState;
  }, [storageKey, persistAcrossRoutes, initialState]);

  // Clear state on unmount if requested
  useEffect(() => {
    return () => {
      if (clearOnUnmount) {
        clearState();
      }
    };
  }, [clearOnUnmount, clearState]);

  return {
    state: stateRef.current,
    setState,
    clearState,
    getState,
    hasState,
  };
}

/**
 * Hook specifically for managing form state across route transitions
 */
export function useFeeFormState<T extends Record<string, any>>(
  formKey: string,
  initialFormData: T
) {
  const {
    state: formData,
    setState: setFormData,
    clearState: clearFormData,
    hasState: hasFormData,
  } = useFeeRouteState(initialFormData, {
    stateKey: `form-${formKey}`,
    persistAcrossRoutes: true,
    clearOnUnmount: false,
  });

  // Update specific form field
  const updateField = useCallback(
    (fieldName: keyof T, value: any) => {
      setFormData((prev) => ({
        ...prev,
        [fieldName]: value,
      }));
    },
    [setFormData]
  );

  // Update multiple fields
  const updateFields = useCallback(
    (updates: Partial<T>) => {
      setFormData((prev) => ({
        ...prev,
        ...updates,
      }));
    },
    [setFormData]
  );

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
  }, [setFormData, initialFormData]);

  // Validate form data
  const validateForm = useCallback(
    (validator: (data: T) => boolean | string[]) => {
      return validator(formData);
    },
    [formData]
  );

  return {
    formData,
    setFormData,
    updateField,
    updateFields,
    resetForm,
    clearFormData,
    hasFormData,
    validateForm,
  };
}

/**
 * Hook for managing component state across route transitions
 */
export function useFeeComponentState<T = any>(
  componentKey: string,
  initialState: T
) {
  return useFeeRouteState(initialState, {
    stateKey: `component-${componentKey}`,
    persistAcrossRoutes: true,
    clearOnUnmount: true,
  });
}

export default useFeeRouteState;
