/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Avatar,
  Popover,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import { MdAdd } from 'react-icons/md';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import InsertDriveFileRoundedIcon from '@mui/icons-material/InsertDriveFileRounded';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { BiLogoAndroid, BiLogoApple } from 'react-icons/bi';
import { RiComputerFill } from 'react-icons/ri';
import { getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { boxShadow } from 'html2canvas/dist/types/css/property-descriptors/box-shadow';
import Popup from '@/components/shared/Popup/Popup';
import CreateLeave from './CreateLeave';
import { Link, useNavigate } from 'react-router-dom';

export const data = [
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Android',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Android',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Android',
  },
  {
    staffName: 'Peter Jack',
    class: 'VII-B',
    date: 'Sep 25th',
    admission: '34765',
    mobileNumber: '+91-8976 756 457',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
];

const LeaveListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
      .staff_card .date {
        color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.grey[500] : props.theme.palette.grey[100]};
      }
      .view_file_btn {
        color: ${(props) => props.theme.palette.grey[500]};
        text-decoration: none;
        font-size: 10px;
        :hover {
          color: ${(props) => props.theme.palette.grey[700]};
        }
      }
    }
  }
`;

function LeaveList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const defualtYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [showFilter, setShowFilter] = useState(false);
  const [createOpen, setCreateOpen] = React.useState(false);

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    // loadStaffList({
    //   ...currentStaffListRequest,
    //   filters: {
    //     ...currentStaffListRequest.filters,
    //     academicId: selectedAcademicId,
    //   },
    // });
  };

  useEffect(() => {
    dispatch(fetchYearList(adminId));
  }, [dispatch, adminId]);

  const [selectedChips, setSelectedChips] = useState(
    data.map((student) => {
      if (student.device === 'Android') {
        return 0;
      }
      if (student.device === 'IOS') {
        return 1;
      }
      return 2;
    })
  );
  // const handleChipClick = (cardIndex, chipIndex) => {
  //   setSelectedChips((prevSelectedChips) => {
  //     const updatedSelectedChips = [...prevSelectedChips];
  //     updatedSelectedChips[cardIndex] = chipIndex;
  //     return updatedSelectedChips;
  //   });
  // };
  // const [anchorEl, setAnchorEl] = useState(null);

  // const handlePopoverOpen = (event) => {
  //   setAnchorEl(event.currentTarget);
  // };

  // const handlePopoverClose = () => {
  //   setAnchorEl(null);
  // };

  const open = Boolean(anchorEl);
  const id = open ? 'avatar-popover' : undefined;

  const LeaveListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'staffName',
        headerLabel: 'Name',
        dataKey: 'staffName',
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
    ],
    []
  );

  return (
    <Page title="Leave List">
      <LeaveListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17} width="100%">
                Leave List
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,

                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
                '@media (max-width: 280px)': {
                  flex: 1,
                },
              }}
            >
              <Button
                onClick={() => setCreateOpen(true)}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
              >
                <MdAdd size="20px" />
                Create
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Teacher
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        // value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        // onChange={handleYearChange}
                        placeholder="Select Teacher"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {/* {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))} */}
                        {['Alex Peter', 'John Mark'].map((i) => (
                          <MenuItem>{i}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DatePickers
                      // name={`staff[${rowIndex}].staffJoinDate`}
                      // value={dayjs(staffRow.staffJoinDate, 'DD/MM/YYYY')}
                      // onChange={(e) => {
                      //   const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                      //   setFieldValue(`staff[${rowIndex}].staffJoinDate`, formattedDate);
                      //   console.log('date::::', formattedDate);
                      // }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DatePickers
                      // name={`staff[${rowIndex}].staffJoinDate`}
                      // value={dayjs(staffRow.staffJoinDate, 'DD/MM/YYYY')}
                      // onChange={(e) => {
                      //   const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                      //   setFieldValue(`staff[${rowIndex}].staffJoinDate`, formattedDate);
                      //   console.log('date::::', formattedDate);
                      // }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" mt={2}>
              <Grid container spacing={2}>
                {data.map((staff, cardIndex) => (
                  <Grid key={staff.admission} item xl={3} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="staff_card"
                      sx={{ border: `1px solid ${theme.palette.grey[300]}`, p: 2, boxShadow: 0 }}
                    >
                      <Stack direction="row" gap={2}>
                        <Avatar sx={{ width: 50, height: 50 }} src={staff.image} />
                        <Stack gap={0.5}>
                          <Typography variant="subtitle2">{staff.staffName}</Typography>
                          <Chip size="small" color="info" sx={{ fontSize: 12 }} label={`Applied on : ${staff.date}`} />
                          <Typography variant="subtitle2" fontSize={10}>
                            {staff.mobileNumber}
                          </Typography>
                        </Stack>
                      </Stack>
                      <Stack gap={1.5} mt={1.5}>
                        <Stack
                          direction="row"
                          justifyContent="space-between"
                          flexWrap="wrap"
                          columnGap={1.5}
                          rowGap={1.5}
                        >
                          <Typography variant="subtitle2" fontSize={10} display="flex" gap={0.5} alignItems="center">
                            <CalendarTodayIcon sx={{ fontSize: 12 }} />
                            <span className="date">From</span>: Sep 27, 2024
                          </Typography>
                          <Typography variant="subtitle2" fontSize={10} display="flex" gap={0.5} alignItems="center">
                            <CalendarTodayIcon sx={{ fontSize: 12 }} />
                            <span className="date">To</span>: Oct 02, 2024
                          </Typography>
                        </Stack>
                        <Typography variant="subtitle2" fontSize={10}>
                          Leave Title : Request For Four Days Leave
                        </Typography>
                        <Card
                          sx={{ boxShadow: 0, border: 1, borderColor: theme.palette.grey[300], p: 1, borderRadius: 1 }}
                        >
                          <Typography variant="body1" color={theme.palette.grey[500]} fontSize={10}>
                            Lorem ipsum dolor sit consec tetur tetur amet adipisicing elit. Numquam iste at
                          </Typography>
                        </Card>
                        <Stack>
                          <Typography pb={0.5} variant="subtitle2" fontSize={10}>
                            Upload File
                          </Typography>
                          <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="space-between"
                            color={theme.palette.grey[500]}
                          >
                            <span style={{ display: 'flex', alignItems: 'center' }}>
                              <InsertDriveFileRoundedIcon />
                              <Typography variant="subtitle1" fontSize={10}>
                                8+
                              </Typography>
                            </span>
                            <Link
                              to="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
                              className="view_file_btn"
                            >
                              View File
                            </Link>
                          </Stack>
                        </Stack>
                        <Stack direction="row" gap={5}>
                          <Button fullWidth variant="contained" color="error">
                            Reject
                          </Button>
                          <Button fullWidth variant="contained" color="success">
                            Approve
                          </Button>
                        </Stack>
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </LeaveListRoot>
      <Popup
        size="sm"
        title="Leave Details Create"
        state={createOpen}
        onClose={() => setCreateOpen(false)}
        popupContent={<CreateLeave onCancel={() => setCreateOpen(false)} />}
      />
    </Page>
  );
}

export default LeaveList;
